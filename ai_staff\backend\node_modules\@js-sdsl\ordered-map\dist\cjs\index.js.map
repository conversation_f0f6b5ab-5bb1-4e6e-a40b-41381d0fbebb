{"version": 3, "sources": ["index.js", "../../../.build-data/copied-source/src/container/TreeContainer/Base/TreeNode.ts", "../../../.build-data/copied-source/src/container/ContainerBase/index.ts", "../../../.build-data/copied-source/src/utils/throwError.ts", "../../../.build-data/copied-source/src/container/TreeContainer/Base/index.ts", "../../../.build-data/copied-source/src/container/TreeContainer/Base/TreeIterator.ts", "../../../.build-data/copied-source/src/container/TreeContainer/OrderedMap.ts"], "names": ["Object", "defineProperty", "exports", "value", "TreeNode", "constructor", "key", "color", "this", "_left", "undefined", "_right", "_parent", "_key", "_value", "_color", "_pre", "preNode", "isRootOrHeader", "pre", "_next", "nextNode", "_rotateLeft", "PP", "V", "R", "_rotateRight", "F", "K", "TreeNodeEnableIndex", "super", "arguments", "_subTreeSize", "parent", "_recount", "ContainerIterator", "iteratorType", "equals", "iter", "_node", "Base", "_length", "length", "size", "empty", "Container", "throwIteratorAccessError", "RangeError", "TreeC<PERSON>r", "cmp", "x", "y", "enableIndex", "_root", "_cmp", "_TreeNodeClass", "_header", "_lowerBound", "curNode", "resNode", "cmpResult", "_upperBound", "_reverseLowerBound", "_reverseUpperBound", "_eraseNodeSelfBalance", "parentNode", "brother", "_eraseNode", "clear", "swapNode", "_inOrderTraversal", "param", "pos", "callback", "nodeList", "index", "stack", "push", "pop", "_insertNodeSelfBalance", "grandParent", "uncle", "GP", "_set", "hint", "minNode", "compareToMin", "maxNode", "compareToMax", "iterNode", "iterCmpRes", "preCmpRes", "_getTreeNodeByKey", "updateKeyByIterator", "node", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "eraseElementByPos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "hasNoRight", "isNormal", "next", "getHeight", "traversal", "Math", "max", "TreeIterator", "header", "root", "isAccessible", "OrderedMapIterator", "container", "pointer", "self", "Proxy", "get", "target", "prop", "set", "_", "newValue", "TypeError", "copy", "OrderedMap", "for<PERSON>ach", "el", "setElement", "begin", "end", "rBegin", "rEnd", "front", "back", "lowerBound", "upperBound", "reverseLowerBound", "reverseUpperBound", "map", "getElementByPos", "find", "getElement<PERSON>y<PERSON>ey", "union", "other", "Symbol", "iterator", "i"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAAEC,OAAO;;;AAEtD,MCCaC;IAOXC,WAAAA,CACEC,GACAH,GACAI,IAAwC;QAN1CC,KAAKC,IAA+BC;QACpCF,KAAMG,IAA+BD;QACrCF,KAAOI,IAA+BF;QAMpCF,KAAKK,IAAOP;QACZE,KAAKM,IAASX;QACdK,KAAKO,IAASR;AACf;IAKDS,CAAAA;QACE,IAAIC,IAA0BT;QAC9B,MAAMU,IAAiBD,EAAQL,EAASA,MAAYK;QACpD,IAAIC,KAAkBD,EAAQF,MAAM,GAAwB;YAC1DE,IAAUA,EAAQN;AACnB,eAAM,IAAIM,EAAQR,GAAO;YACxBQ,IAAUA,EAAQR;YAClB,OAAOQ,EAAQN,GAAQ;gBACrBM,IAAUA,EAAQN;AACnB;AACF,eAAM;YAEL,IAAIO,GAAgB;gBAClB,OAAOD,EAAQL;AAChB;YACD,IAAIO,IAAMF,EAAQL;YAClB,OAAOO,EAAIV,MAAUQ,GAAS;gBAC5BA,IAAUE;gBACVA,IAAMF,EAAQL;AACf;YACDK,IAAUE;AACX;QACD,OAAOF;AACR;IAKDG,CAAAA;QACE,IAAIC,IAA2Bb;QAC/B,IAAIa,EAASV,GAAQ;YACnBU,IAAWA,EAASV;YACpB,OAAOU,EAASZ,GAAO;gBACrBY,IAAWA,EAASZ;AACrB;YACD,OAAOY;AACR,eAAM;YACL,IAAIF,IAAME,EAAST;YACnB,OAAOO,EAAIR,MAAWU,GAAU;gBAC9BA,IAAWF;gBACXA,IAAME,EAAST;AAChB;YACD,IAAIS,EAASV,MAAWQ,GAAK;gBAC3B,OAAOA;ADPT,mBCQO,OAAOE;AACf;AACF;IAKDC,CAAAA;QACE,MAAMC,IAAKf,KAAKI;QAChB,MAAMY,IAAIhB,KAAKG;QACf,MAAMc,IAAID,EAAEf;QAEZ,IAAIc,EAAGX,MAAYJ,MAAMe,EAAGX,IAAUY,QACjC,IAAID,EAAGd,MAAUD,MAAMe,EAAGd,IAAQe,QAClCD,EAAGZ,IAASa;QAEjBA,EAAEZ,IAAUW;QACZC,EAAEf,IAAQD;QAEVA,KAAKI,IAAUY;QACfhB,KAAKG,IAASc;QAEd,IAAIA,GAAGA,EAAEb,IAAUJ;QAEnB,OAAOgB;AACR;IAKDE,CAAAA;QACE,MAAMH,IAAKf,KAAKI;QAChB,MAAMe,IAAInB,KAAKC;QACf,MAAMmB,IAAID,EAAEhB;QAEZ,IAAIY,EAAGX,MAAYJ,MAAMe,EAAGX,IAAUe,QACjC,IAAIJ,EAAGd,MAAUD,MAAMe,EAAGd,IAAQkB,QAClCJ,EAAGZ,IAASgB;QAEjBA,EAAEf,IAAUW;QACZI,EAAEhB,IAASH;QAEXA,KAAKI,IAAUe;QACfnB,KAAKC,IAAQmB;QAEb,IAAIA,GAAGA,EAAEhB,IAAUJ;QAEnB,OAAOmB;AACR;;;AAGG,MAAOE,4BAAkCzB;IAA/CC,WAAAA;QDrBIyB,SAASC;QCsBXvB,KAAYwB,IAAG;AA8BjB;IAzBEV,CAAAA;QACE,MAAMW,IAASH,MAAMR;QACrBd,KAAK0B;QACLD,EAAOC;QACP,OAAOD;AACR;IAKDP,CAAAA;QACE,MAAMO,IAASH,MAAMJ;QACrBlB,KAAK0B;QACLD,EAAOC;QACP,OAAOD;AACR;IACDC,CAAAA;QACE1B,KAAKwB,IAAe;QACpB,IAAIxB,KAAKC,GAAO;YACdD,KAAKwB,KAAiBxB,KAAKC,EAAoCuB;AAChE;QACD,IAAIxB,KAAKG,GAAQ;YACfH,KAAKwB,KAAiBxB,KAAKG,EAAqCqB;AACjE;AACF;;;ADjBH,ME7HsBG;IAkBpB9B,WAAAA,CAAsB+B,IAAkC;QACtD5B,KAAK4B,eAAeA;AACrB;IAODC,MAAAA,CAAOC;QACL,OAAO9B,KAAK+B,MAAUD,EAAKC;AAC5B;;;AFiHH,ME9DsBC;IAAtBnC,WAAAA;QAKYG,KAAOiC,IAAG;AAmCtB;IA5BE,UAAIC;QACF,OAAOlC,KAAKiC;AACb;IAODE,IAAAA;QACE,OAAOnC,KAAKiC;AACb;IAODG,KAAAA;QACE,OAAOpC,KAAKiC,MAAY;AACzB;;;AAUG,MAAgBI,kBAAqBL;;AF8D3C,SG5LgBM;IACd,MAAM,IAAIC,WAAW;AACtB;;ACAD,MAAeC,sBAA4BH;IAqBzCxC,WAAAA,CACE4C,IACA,SAAUC,GAAMC;QACd,IAAID,IAAIC,GAAG,QAAQ;QACnB,IAAID,IAAIC,GAAG,OAAO;QAClB,OAAO;AJ4KX,OI1KEC,IAAc;QAEdtB;QArBQtB,KAAK6C,IAA+B3C;QAsB5CF,KAAK8C,IAAOL;QACZzC,KAAK4C,cAAcA;QACnB5C,KAAK+C,IAAiBH,IAAcvB,sBAAsBzB;QAC1DI,KAAKgD,IAAU,IAAIhD,KAAK+C;AACzB;IAISE,CAAAA,CAAYC,GAAqCpD;QACzD,IAAIqD,IAAUnD,KAAKgD;QACnB,OAAOE,GAAS;YACd,MAAME,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;YAC3C,IAAIsD,IAAY,GAAG;gBACjBF,IAAUA,EAAQ/C;AACnB,mBAAM,IAAIiD,IAAY,GAAG;gBACxBD,IAAUD;gBACVA,IAAUA,EAAQjD;AJ8KpB,mBI7KO,OAAOiD;AACf;QACD,OAAOC;AACR;IAISE,CAAAA,CAAYH,GAAqCpD;QACzD,IAAIqD,IAAUnD,KAAKgD;QACnB,OAAOE,GAAS;YACd,MAAME,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;YAC3C,IAAIsD,KAAa,GAAG;gBAClBF,IAAUA,EAAQ/C;AACnB,mBAAM;gBACLgD,IAAUD;gBACVA,IAAUA,EAAQjD;AACnB;AACF;QACD,OAAOkD;AACR;IAISG,CAAAA,CAAmBJ,GAAqCpD;QAChE,IAAIqD,IAAUnD,KAAKgD;QACnB,OAAOE,GAAS;YACd,MAAME,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;YAC3C,IAAIsD,IAAY,GAAG;gBACjBD,IAAUD;gBACVA,IAAUA,EAAQ/C;AACnB,mBAAM,IAAIiD,IAAY,GAAG;gBACxBF,IAAUA,EAAQjD;AJ8KpB,mBI7KO,OAAOiD;AACf;QACD,OAAOC;AACR;IAISI,CAAAA,CAAmBL,GAAqCpD;QAChE,IAAIqD,IAAUnD,KAAKgD;QACnB,OAAOE,GAAS;YACd,MAAME,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;YAC3C,IAAIsD,IAAY,GAAG;gBACjBD,IAAUD;gBACVA,IAAUA,EAAQ/C;AACnB,mBAAM;gBACL+C,IAAUA,EAAQjD;AACnB;AACF;QACD,OAAOkD;AACR;IAISK,CAAAA,CAAsBN;QAC9B,OAAO,MAAM;YACX,MAAMO,IAAaP,EAAQ9C;YAC3B,IAAIqD,MAAezD,KAAKgD,GAAS;YACjC,IAAIE,EAAQ3C,MAAM,GAAwB;gBACxC2C,EAAQ3C,IAAM;gBACd;AACD;YACD,IAAI2C,MAAYO,EAAWxD,GAAO;gBAChC,MAAMyD,IAAUD,EAAWtD;gBAC3B,IAAIuD,EAAQnD,MAAM,GAAwB;oBACxCmD,EAAQnD,IAAM;oBACdkD,EAAWlD,IAAM;oBACjB,IAAIkD,MAAezD,KAAK6C,GAAO;wBAC7B7C,KAAK6C,IAAQY,EAAW3C;AACzB,2BAAM2C,EAAW3C;AACnB,uBAAM;oBACL,IAAI4C,EAAQvD,KAAUuD,EAAQvD,EAAOI,MAAM,GAAwB;wBACjEmD,EAAQnD,IAASkD,EAAWlD;wBAC5BkD,EAAWlD,IAAM;wBACjBmD,EAAQvD,EAAOI,IAAM;wBACrB,IAAIkD,MAAezD,KAAK6C,GAAO;4BAC7B7C,KAAK6C,IAAQY,EAAW3C;AACzB,+BAAM2C,EAAW3C;wBAClB;AACD,2BAAM,IAAI4C,EAAQzD,KAASyD,EAAQzD,EAAMM,MAAM,GAAwB;wBACtEmD,EAAQnD,IAAM;wBACdmD,EAAQzD,EAAMM,IAAM;wBACpBmD,EAAQxC;AACT,2BAAM;wBACLwC,EAAQnD,IAAM;wBACd2C,IAAUO;AACX;AACF;AACF,mBAAM;gBACL,MAAMC,IAAUD,EAAWxD;gBAC3B,IAAIyD,EAAQnD,MAAM,GAAwB;oBACxCmD,EAAQnD,IAAM;oBACdkD,EAAWlD,IAAM;oBACjB,IAAIkD,MAAezD,KAAK6C,GAAO;wBAC7B7C,KAAK6C,IAAQY,EAAWvC;AACzB,2BAAMuC,EAAWvC;AACnB,uBAAM;oBACL,IAAIwC,EAAQzD,KAASyD,EAAQzD,EAAMM,MAAM,GAAwB;wBAC/DmD,EAAQnD,IAASkD,EAAWlD;wBAC5BkD,EAAWlD,IAAM;wBACjBmD,EAAQzD,EAAMM,IAAM;wBACpB,IAAIkD,MAAezD,KAAK6C,GAAO;4BAC7B7C,KAAK6C,IAAQY,EAAWvC;AACzB,+BAAMuC,EAAWvC;wBAClB;AACD,2BAAM,IAAIwC,EAAQvD,KAAUuD,EAAQvD,EAAOI,MAAM,GAAwB;wBACxEmD,EAAQnD,IAAM;wBACdmD,EAAQvD,EAAOI,IAAM;wBACrBmD,EAAQ5C;AACT,2BAAM;wBACL4C,EAAQnD,IAAM;wBACd2C,IAAUO;AACX;AACF;AACF;AACF;AACF;IAISE,CAAAA,CAAWT;QACnB,IAAIlD,KAAKiC,MAAY,GAAG;YACtBjC,KAAK4D;YACL;AACD;QACD,IAAIC,IAAWX;QACf,OAAOW,EAAS5D,KAAS4D,EAAS1D,GAAQ;YACxC,IAAI0D,EAAS1D,GAAQ;gBACnB0D,IAAWA,EAAS1D;gBACpB,OAAO0D,EAAS5D,GAAO4D,IAAWA,EAAS5D;AAC5C,mBAAM;gBACL4D,IAAWA,EAAS5D;AACrB;YACD,MAAMH,IAAMoD,EAAQ7C;YACpB6C,EAAQ7C,IAAOwD,EAASxD;YACxBwD,EAASxD,IAAOP;YAChB,MAAMH,IAAQuD,EAAQ5C;YACtB4C,EAAQ5C,IAASuD,EAASvD;YAC1BuD,EAASvD,IAASX;YAClBuD,IAAUW;AACX;QACD,IAAI7D,KAAKgD,EAAQ/C,MAAU4D,GAAU;YACnC7D,KAAKgD,EAAQ/C,IAAQ4D,EAASzD;AJ8KhC,eI7KO,IAAIJ,KAAKgD,EAAQ7C,MAAW0D,GAAU;YAC3C7D,KAAKgD,EAAQ7C,IAAS0D,EAASzD;AAChC;QACDJ,KAAKwD,EAAsBK;QAC3B,IAAIzD,IAAUyD,EAASzD;QACvB,IAAIyD,MAAazD,EAAQH,GAAO;YAC9BG,EAAQH,IAAQC;AACjB,eAAME,EAAQD,IAASD;QACxBF,KAAKiC,KAAW;QAChBjC,KAAK6C,EAAOtC,IAAM;QAClB,IAAIP,KAAK4C,aAAa;YACpB,OAAOxC,MAAYJ,KAAKgD,GAAS;gBAC/B5C,EAAQoB,KAAgB;gBACxBpB,IAAUA,EAAQA;AACnB;AACF;AACF;IASS0D,CAAAA,CACRC;QAEA,MAAMC,WAAaD,MAAU,WAAWA,IAAQ7D;QAChD,MAAM+D,WAAkBF,MAAU,aAAaA,IAAQ7D;QACvD,MAAMgE,WAAkBH,MAAU,cAAgC,KAAK7D;QACvE,IAAIiE,IAAQ;QACZ,IAAIjB,IAAUlD,KAAK6C;QACnB,MAAMuB,IAA0B;QAChC,OAAOA,EAAMlC,UAAUgB,GAAS;YAC9B,IAAIA,GAAS;gBACXkB,EAAMC,KAAKnB;gBACXA,IAAUA,EAAQjD;AACnB,mBAAM;gBACLiD,IAAUkB,EAAME;gBAChB,IAAIH,MAAUH,GAAK,OAAOd;gBAC1BgB,KAAYA,EAASG,KAAKnB;gBAC1Be,KAAYA,EAASf,GAASiB,GAAOnE;gBACrCmE,KAAS;gBACTjB,IAAUA,EAAQ/C;AACnB;AACF;QACD,OAAO+D;AACR;IAISK,CAAAA,CAAuBrB;QAC/B,OAAO,MAAM;YACX,MAAMO,IAAaP,EAAQ9C;YAC3B,IAAIqD,EAAWlD,MAA8B,GAAE;YAC/C,MAAMiE,IAAcf,EAAWrD;YAC/B,IAAIqD,MAAee,EAAYvE,GAAO;gBACpC,MAAMwE,IAAQD,EAAYrE;gBAC1B,IAAIsE,KAASA,EAAMlE,MAAM,GAAwB;oBAC/CkE,EAAMlE,IAASkD,EAAWlD,IAAM;oBAChC,IAAIiE,MAAgBxE,KAAK6C,GAAO;oBAChC2B,EAAYjE,IAAM;oBAClB2C,IAAUsB;oBACV;AACD,uBAAM,IAAItB,MAAYO,EAAWtD,GAAQ;oBACxC+C,EAAQ3C,IAAM;oBACd,IAAI2C,EAAQjD,GAAO;wBACjBiD,EAAQjD,EAAMG,IAAUqD;AACzB;oBACD,IAAIP,EAAQ/C,GAAQ;wBAClB+C,EAAQ/C,EAAOC,IAAUoE;AAC1B;oBACDf,EAAWtD,IAAS+C,EAAQjD;oBAC5BuE,EAAYvE,IAAQiD,EAAQ/C;oBAC5B+C,EAAQjD,IAAQwD;oBAChBP,EAAQ/C,IAASqE;oBACjB,IAAIA,MAAgBxE,KAAK6C,GAAO;wBAC9B7C,KAAK6C,IAAQK;wBACblD,KAAKgD,EAAQ5C,IAAU8C;AACxB,2BAAM;wBACL,MAAMwB,IAAKF,EAAYpE;wBACvB,IAAIsE,EAAGzE,MAAUuE,GAAa;4BAC5BE,EAAGzE,IAAQiD;AACZ,+BAAMwB,EAAGvE,IAAS+C;AACpB;oBACDA,EAAQ9C,IAAUoE,EAAYpE;oBAC9BqD,EAAWrD,IAAU8C;oBACrBsB,EAAYpE,IAAU8C;oBACtBsB,EAAYjE,IAAM;AACnB,uBAAM;oBACLkD,EAAWlD,IAAM;oBACjB,IAAIiE,MAAgBxE,KAAK6C,GAAO;wBAC9B7C,KAAK6C,IAAQ2B,EAAYtD;AAC1B,2BAAMsD,EAAYtD;oBACnBsD,EAAYjE,IAAM;oBAClB;AACD;AACF,mBAAM;gBACL,MAAMkE,IAAQD,EAAYvE;gBAC1B,IAAIwE,KAASA,EAAMlE,MAAM,GAAwB;oBAC/CkE,EAAMlE,IAASkD,EAAWlD,IAAM;oBAChC,IAAIiE,MAAgBxE,KAAK6C,GAAO;oBAChC2B,EAAYjE,IAAM;oBAClB2C,IAAUsB;oBACV;AACD,uBAAM,IAAItB,MAAYO,EAAWxD,GAAO;oBACvCiD,EAAQ3C,IAAM;oBACd,IAAI2C,EAAQjD,GAAO;wBACjBiD,EAAQjD,EAAMG,IAAUoE;AACzB;oBACD,IAAItB,EAAQ/C,GAAQ;wBAClB+C,EAAQ/C,EAAOC,IAAUqD;AAC1B;oBACDe,EAAYrE,IAAS+C,EAAQjD;oBAC7BwD,EAAWxD,IAAQiD,EAAQ/C;oBAC3B+C,EAAQjD,IAAQuE;oBAChBtB,EAAQ/C,IAASsD;oBACjB,IAAIe,MAAgBxE,KAAK6C,GAAO;wBAC9B7C,KAAK6C,IAAQK;wBACblD,KAAKgD,EAAQ5C,IAAU8C;AACxB,2BAAM;wBACL,MAAMwB,IAAKF,EAAYpE;wBACvB,IAAIsE,EAAGzE,MAAUuE,GAAa;4BAC5BE,EAAGzE,IAAQiD;AACZ,+BAAMwB,EAAGvE,IAAS+C;AACpB;oBACDA,EAAQ9C,IAAUoE,EAAYpE;oBAC9BqD,EAAWrD,IAAU8C;oBACrBsB,EAAYpE,IAAU8C;oBACtBsB,EAAYjE,IAAM;AACnB,uBAAM;oBACLkD,EAAWlD,IAAM;oBACjB,IAAIiE,MAAgBxE,KAAK6C,GAAO;wBAC9B7C,KAAK6C,IAAQ2B,EAAY1D;AAC1B,2BAAM0D,EAAY1D;oBACnB0D,EAAYjE,IAAM;oBAClB;AACD;AACF;YACD,IAAIP,KAAK4C,aAAa;gBACQa,EAAY/B;gBACZ8C,EAAa9C;gBACbwB,EAASxB;AACtC;YACD;AACD;AACF;IAISiD,CAAAA,CAAK7E,GAAQH,GAAWiF;QAChC,IAAI5E,KAAK6C,MAAU3C,WAAW;YAC5BF,KAAKiC,KAAW;YAChBjC,KAAK6C,IAAQ,IAAI7C,KAAK+C,EAAejD,GAAKH,GAAK;YAC/CK,KAAK6C,EAAMzC,IAAUJ,KAAKgD;YAC1BhD,KAAKgD,EAAQ5C,IAAUJ,KAAKgD,EAAQ/C,IAAQD,KAAKgD,EAAQ7C,IAASH,KAAK6C;YACvE,OAAO7C,KAAKiC;AACb;QACD,IAAIiB;QACJ,MAAM2B,IAAU7E,KAAKgD,EAAQ/C;QAC7B,MAAM6E,IAAe9E,KAAK8C,EAAK+B,EAAQxE,GAAOP;QAC9C,IAAIgF,MAAiB,GAAG;YACtBD,EAAQvE,IAASX;YACjB,OAAOK,KAAKiC;AACb,eAAM,IAAI6C,IAAe,GAAG;YAC3BD,EAAQ5E,IAAQ,IAAID,KAAK+C,EAAejD,GAAKH;YAC7CkF,EAAQ5E,EAAMG,IAAUyE;YACxB3B,IAAU2B,EAAQ5E;YAClBD,KAAKgD,EAAQ/C,IAAQiD;AACtB,eAAM;YACL,MAAM6B,IAAU/E,KAAKgD,EAAQ7C;YAC7B,MAAM6E,IAAehF,KAAK8C,EAAKiC,EAAQ1E,GAAOP;YAC9C,IAAIkF,MAAiB,GAAG;gBACtBD,EAAQzE,IAASX;gBACjB,OAAOK,KAAKiC;AACb,mBAAM,IAAI+C,IAAe,GAAG;gBAC3BD,EAAQ5E,IAAS,IAAIH,KAAK+C,EAAejD,GAAKH;gBAC9CoF,EAAQ5E,EAAOC,IAAU2E;gBACzB7B,IAAU6B,EAAQ5E;gBAClBH,KAAKgD,EAAQ7C,IAAS+C;AACvB,mBAAM;gBACL,IAAI0B,MAAS1E,WAAW;oBACtB,MAAM+E,IAAWL,EAAK7C;oBACtB,IAAIkD,MAAajF,KAAKgD,GAAS;wBAC7B,MAAMkC,IAAalF,KAAK8C,EAAKmC,EAAS5E,GAAOP;wBAC7C,IAAIoF,MAAe,GAAG;4BACpBD,EAAS3E,IAASX;4BAClB,OAAOK,KAAKiC;AACb,+BAAiC,IAAIiD,IAAa,GAAG;4BACpD,MAAMzE,IAAUwE,EAASzE;4BACzB,MAAM2E,IAAYnF,KAAK8C,EAAKrC,EAAQJ,GAAOP;4BAC3C,IAAIqF,MAAc,GAAG;gCACnB1E,EAAQH,IAASX;gCACjB,OAAOK,KAAKiC;AACb,mCAAM,IAAIkD,IAAY,GAAG;gCACxBjC,IAAU,IAAIlD,KAAK+C,EAAejD,GAAKH;gCACvC,IAAIc,EAAQN,MAAWD,WAAW;oCAChCO,EAAQN,IAAS+C;oCACjBA,EAAQ9C,IAAUK;AACnB,uCAAM;oCACLwE,EAAShF,IAAQiD;oCACjBA,EAAQ9C,IAAU6E;AACnB;AACF;AACF;AACF;AACF;gBACD,IAAI/B,MAAYhD,WAAW;oBACzBgD,IAAUlD,KAAK6C;oBACf,OAAO,MAAM;wBACX,MAAMO,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;wBAC3C,IAAIsD,IAAY,GAAG;4BACjB,IAAIF,EAAQjD,MAAUC,WAAW;gCAC/BgD,EAAQjD,IAAQ,IAAID,KAAK+C,EAAejD,GAAKH;gCAC7CuD,EAAQjD,EAAMG,IAAU8C;gCACxBA,IAAUA,EAAQjD;gCAClB;AACD;4BACDiD,IAAUA,EAAQjD;AACnB,+BAAM,IAAImD,IAAY,GAAG;4BACxB,IAAIF,EAAQ/C,MAAWD,WAAW;gCAChCgD,EAAQ/C,IAAS,IAAIH,KAAK+C,EAAejD,GAAKH;gCAC9CuD,EAAQ/C,EAAOC,IAAU8C;gCACzBA,IAAUA,EAAQ/C;gCAClB;AACD;4BACD+C,IAAUA,EAAQ/C;AACnB,+BAAM;4BACL+C,EAAQ5C,IAASX;4BACjB,OAAOK,KAAKiC;AACb;AACF;AACF;AACF;AACF;QACD,IAAIjC,KAAK4C,aAAa;YACpB,IAAInB,IAASyB,EAAQ9C;YACrB,OAAOqB,MAAWzB,KAAKgD,GAAS;gBAC9BvB,EAAOD,KAAgB;gBACvBC,IAASA,EAAOrB;AACjB;AACF;QACDJ,KAAKuE,EAAuBrB;QAC5BlD,KAAKiC,KAAW;QAChB,OAAOjC,KAAKiC;AACb;IAISmD,CAAAA,CAAkBlC,GAAqCpD;QAC/D,OAAOoD,GAAS;YACd,MAAME,IAAYpD,KAAK8C,EAAKI,EAAQ7C,GAAOP;YAC3C,IAAIsD,IAAY,GAAG;gBACjBF,IAAUA,EAAQ/C;AACnB,mBAAM,IAAIiD,IAAY,GAAG;gBACxBF,IAAUA,EAAQjD;AJuKpB,mBItKO,OAAOiD;AACf;QACD,OAAOA,KAAWlD,KAAKgD;AACxB;IACDY,KAAAA;QACE5D,KAAKiC,IAAU;QACfjC,KAAK6C,IAAQ3C;QACbF,KAAKgD,EAAQ5C,IAAUF;QACvBF,KAAKgD,EAAQ/C,IAAQD,KAAKgD,EAAQ7C,IAASD;AAC5C;IAWDmF,mBAAAA,CAAoBvD,GAA0BhC;QAC5C,MAAMwF,IAAOxD,EAAKC;QAClB,IAAIuD,MAAStF,KAAKgD,GAAS;YACzBV;AACD;QACD,IAAItC,KAAKiC,MAAY,GAAG;YACtBqD,EAAKjF,IAAOP;YACZ,OAAO;AACR;QACD,MAAMyF,IAAUD,EAAK1E,IAAQP;QAC7B,IAAIiF,MAAStF,KAAKgD,EAAQ/C,GAAO;YAC/B,IAAID,KAAK8C,EAAKyC,GAASzF,KAAO,GAAG;gBAC/BwF,EAAKjF,IAAOP;gBACZ,OAAO;AACR;YACD,OAAO;AACR;QACD,MAAM0F,IAASF,EAAK9E,IAAOH;QAC3B,IAAIiF,MAAStF,KAAKgD,EAAQ7C,GAAQ;YAChC,IAAIH,KAAK8C,EAAK0C,GAAQ1F,KAAO,GAAG;gBAC9BwF,EAAKjF,IAAOP;gBACZ,OAAO;AACR;YACD,OAAO;AACR;QACD,IACEE,KAAK8C,EAAK0C,GAAQ1F,MAAQ,KAC1BE,KAAK8C,EAAKyC,GAASzF,MAAQ,GAC3B,OAAO;QACTwF,EAAKjF,IAAOP;QACZ,OAAO;AACR;IACD2F,iBAAAA,CAAkBzB;QACU,IAAAA,IAAG,KAAHA,IAAQhE,KAAKiC,IAtfP,GAAA;YAAE,MAAU,IAAIM;AACjD;QAsfC,MAAM+C,IAAOtF,KAAK8D,EAAkBE;QACpChE,KAAK2D,EAAW2B;QAChB,OAAOtF,KAAKiC;AACb;IAMDyD,iBAAAA,CAAkB5F;QAChB,IAAIE,KAAKiC,MAAY,GAAG,OAAO;QAC/B,MAAMiB,IAAUlD,KAAKoF,EAAkBpF,KAAK6C,GAAO/C;QACnD,IAAIoD,MAAYlD,KAAKgD,GAAS,OAAO;QACrChD,KAAK2D,EAAWT;QAChB,OAAO;AACR;IACDyC,sBAAAA,CAAuB7D;QACrB,MAAMwD,IAAOxD,EAAKC;QAClB,IAAIuD,MAAStF,KAAKgD,GAAS;YACzBV;AACD;QACD,MAAMsD,IAAaN,EAAKnF,MAAWD;QACnC,MAAM2F,IAAW/D,EAAKF,iBAAY;QAElC,IAAIiE,GAAU;YAEZ,IAAID,GAAY9D,EAAKgE;AACtB,eAAM;YAGL,KAAKF,KAAcN,EAAKrF,MAAUC,WAAW4B,EAAKgE;AACnD;QACD9F,KAAK2D,EAAW2B;QAChB,OAAOxD;AACR;IAKDiE,SAAAA;QACE,IAAI/F,KAAKiC,MAAY,GAAG,OAAO;QAC/B,SAAS+D,UAAU9C;YACjB,KAAKA,GAAS,OAAO;YACrB,OAAO+C,KAAKC,IAAIF,UAAU9C,EAAQjD,IAAQ+F,UAAU9C,EAAQ/C,MAAW;AACxE;QACD,OAAO6F,UAAUhG,KAAK6C;AACvB;;;ACriBH,MAAesD,qBAA2BxE;IAaxC9B,WAAAA,CACEyF,GACAc,GACAxE;QAEAN,MAAMM;QACN5B,KAAK+B,IAAQuD;QACbtF,KAAKgD,IAAUoD;QACf,IAAIpG,KAAK4B,iBAAY,GAA0B;YAC7C5B,KAAKW,MAAM;gBACT,IAAIX,KAAK+B,MAAU/B,KAAKgD,EAAQ/C,GAAO;oBACrCqC;AACD;gBACDtC,KAAK+B,IAAQ/B,KAAK+B,EAAMvB;gBACxB,OAAOR;ALisBT;YK9rBAA,KAAK8F,OAAO;gBACV,IAAI9F,KAAK+B,MAAU/B,KAAKgD,GAAS;oBAC/BV;AACD;gBACDtC,KAAK+B,IAAQ/B,KAAK+B,EAAMnB;gBACxB,OAAOZ;ALgsBT;AK9rBD,eAAM;YACLA,KAAKW,MAAM;gBACT,IAAIX,KAAK+B,MAAU/B,KAAKgD,EAAQ7C,GAAQ;oBACtCmC;AACD;gBACDtC,KAAK+B,IAAQ/B,KAAK+B,EAAMnB;gBACxB,OAAOZ;ALgsBT;YK7rBAA,KAAK8F,OAAO;gBACV,IAAI9F,KAAK+B,MAAU/B,KAAKgD,GAAS;oBAC/BV;AACD;gBACDtC,KAAK+B,IAAQ/B,KAAK+B,EAAMvB;gBACxB,OAAOR;AL+rBT;AK7rBD;AACF;IAUD,SAAImE;QACF,IAAIpC,IAAQ/B,KAAK+B;QACjB,MAAMsE,IAAOrG,KAAKgD,EAAQ5C;QAC1B,IAAI2B,MAAU/B,KAAKgD,GAAS;YAC1B,IAAIqD,GAAM;gBACR,OAAOA,EAAK7E,IAAe;AAC5B;YACD,OAAO;AACR;QACD,IAAI2C,IAAQ;QACZ,IAAIpC,EAAM9B,GAAO;YACfkE,KAAUpC,EAAM9B,EAAoCuB;AACrD;QACD,OAAOO,MAAUsE,GAAM;YACrB,MAAMjG,IAAU2B,EAAM3B;YACtB,IAAI2B,MAAU3B,EAAQD,GAAQ;gBAC5BgE,KAAS;gBACT,IAAI/D,EAAQH,GAAO;oBACjBkE,KAAU/D,EAAQH,EAAoCuB;AACvD;AACF;YACDO,IAAQ3B;AACT;QACD,OAAO+D;AACR;IACDmC,YAAAA;QACE,OAAOtG,KAAK+B,MAAU/B,KAAKgD;AAC5B;;;AC1FH,MAAMuD,2BAAiCJ;IAErCtG,WAAAA,CACEyF,GACAc,GACAI,GACA5E;QAEAN,MAAMgE,GAAMc,GAAQxE;QACpB5B,KAAKwG,YAAYA;AAClB;IACD,WAAIC;QACF,IAAIzG,KAAK+B,MAAU/B,KAAKgD,GAAS;YAC/BV;AACD;QACD,MAAMoE,IAAO1G;QACb,OAAO,IAAI2G,MAAuB,IAAI;YACpCC,GAAAA,CAAIC,GAAQC;gBACV,IAAIA,MAAS,KAAK,OAAOJ,EAAK3E,EAAM1B,QAC/B,IAAIyG,MAAS,KAAK,OAAOJ,EAAK3E,EAAMzB;gBACzCuG,EAAO,KAAKH,EAAK3E,EAAM1B;gBACvBwG,EAAO,KAAKH,EAAK3E,EAAMzB;gBACvB,OAAOuG,EAAOC;ANqxBhB;YMnxBAC,GAAAA,CAAIC,GAAGF,GAAWG;gBAChB,IAAIH,MAAS,KAAK;oBAChB,MAAM,IAAII,UAAU;AACrB;gBACDR,EAAK3E,EAAMzB,IAAS2G;gBACpB,OAAO;AACR;;AAEJ;IACDE,IAAAA;QACE,OAAO,IAAIZ,mBACTvG,KAAK+B,GACL/B,KAAKgD,GACLhD,KAAKwG,WACLxG,KAAK4B;AAER;;;AAOH,MAAMwF,mBAAyB5E;IAW7B3C,WAAAA,CACE2G,IAAmC,IACnC/D,GACAG;QAEAtB,MAAMmB,GAAKG;QACX,MAAM8D,IAAO1G;QACbwG,EAAUa,SAAQ,SAAUC;YAC1BZ,EAAKa,WAAWD,EAAG,IAAIA,EAAG;AAC3B;AACF;IACDE,KAAAA;QACE,OAAO,IAAIjB,mBAAyBvG,KAAKgD,EAAQ/C,KAASD,KAAKgD,GAAShD,KAAKgD,GAAShD;AACvF;IACDyH,GAAAA;QACE,OAAO,IAAIlB,mBAAyBvG,KAAKgD,GAAShD,KAAKgD,GAAShD;AACjE;IACD0H,MAAAA;QACE,OAAO,IAAInB,mBACTvG,KAAKgD,EAAQ7C,KAAUH,KAAKgD,GAC5BhD,KAAKgD,GACLhD,MAAI;AAGP;IACD2H,IAAAA;QACE,OAAO,IAAIpB,mBAAyBvG,KAAKgD,GAAShD,KAAKgD,GAAShD,MAAI;AACrE;IACD4H,KAAAA;QACE,IAAI5H,KAAKiC,MAAY,GAAG;QACxB,MAAM4C,IAAU7E,KAAKgD,EAAQ/C;QAC7B,OAAe,EAAC4E,EAAQxE,GAAMwE,EAAQvE;AACvC;IACDuH,IAAAA;QACE,IAAI7H,KAAKiC,MAAY,GAAG;QACxB,MAAM8C,IAAU/E,KAAKgD,EAAQ7C;QAC7B,OAAe,EAAC4E,EAAQ1E,GAAM0E,EAAQzE;AACvC;IACDwH,UAAAA,CAAWhI;QACT,MAAMqD,IAAUnD,KAAKiD,EAAYjD,KAAK6C,GAAO/C;QAC7C,OAAO,IAAIyG,mBAAyBpD,GAASnD,KAAKgD,GAAShD;AAC5D;IACD+H,UAAAA,CAAWjI;QACT,MAAMqD,IAAUnD,KAAKqD,EAAYrD,KAAK6C,GAAO/C;QAC7C,OAAO,IAAIyG,mBAAyBpD,GAASnD,KAAKgD,GAAShD;AAC5D;IACDgI,iBAAAA,CAAkBlI;QAChB,MAAMqD,IAAUnD,KAAKsD,EAAmBtD,KAAK6C,GAAO/C;QACpD,OAAO,IAAIyG,mBAAyBpD,GAASnD,KAAKgD,GAAShD;AAC5D;IACDiI,iBAAAA,CAAkBnI;QAChB,MAAMqD,IAAUnD,KAAKuD,EAAmBvD,KAAK6C,GAAO/C;QACpD,OAAO,IAAIyG,mBAAyBpD,GAASnD,KAAKgD,GAAShD;AAC5D;IACDqH,OAAAA,CAAQpD;QACNjE,KAAK8D,GAAkB,SAAUwB,GAAMnB,GAAO+D;YAC5CjE,EAAiB,EAACqB,EAAKjF,GAAMiF,EAAKhF,KAAS6D,GAAO+D;AACnD;AACF;IAaDX,UAAAA,CAAWzH,GAAQH,GAAUiF;QAC3B,OAAO5E,KAAK2E,EAAK7E,GAAKH,GAAOiF;AAC9B;IACDuD,eAAAA,CAAgBnE;QACY,IAAAA,IAAG,KAAHA,IAAQhE,KAAKiC,IArIf,GAAA;YAAC,MAAU,IAAIM;AAC1C;QAqIG,MAAM+C,IAAOtF,KAAK8D,EAAkBE;QACpC,OAAe,EAACsB,EAAKjF,GAAMiF,EAAKhF;AACjC;IACD8H,IAAAA,CAAKtI;QACH,MAAMoD,IAAUlD,KAAKoF,EAAkBpF,KAAK6C,GAAO/C;QACnD,OAAO,IAAIyG,mBAAyBrD,GAASlD,KAAKgD,GAAShD;AAC5D;IAODqI,eAAAA,CAAgBvI;QACd,MAAMoD,IAAUlD,KAAKoF,EAAkBpF,KAAK6C,GAAO/C;QACnD,OAAOoD,EAAQ5C;AAChB;IACDgI,KAAAA,CAAMC;QACJ,MAAM7B,IAAO1G;QACbuI,EAAMlB,SAAQ,SAAUC;YACtBZ,EAAKa,WAAWD,EAAG,IAAIA,EAAG;AAC3B;QACD,OAAOtH,KAAKiC;AACb;IACD,GAAGuG,OAAOC;QACR,MAAMvG,IAASlC,KAAKiC;QACpB,MAAMiC,IAAWlE,KAAK8D;QACtB,KAAK,IAAI4E,IAAI,GAAGA,IAAIxG,KAAUwG,GAAG;YAC/B,MAAMpD,IAAOpB,EAASwE;kBACR,EAACpD,EAAKjF,GAAMiF,EAAKhF;AAChC;AACF;;;ANwwBHZ,QAAQ0H,aAAaA", "file": "index.js", "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nclass TreeNode {\n  constructor(key, value, color = 1 /* TreeNodeColor.RED */) {\n    this._left = undefined;\n    this._right = undefined;\n    this._parent = undefined;\n    this._key = key;\n    this._value = value;\n    this._color = color;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  _pre() {\n    let preNode = this;\n    const isRootOrHeader = preNode._parent._parent === preNode;\n    if (isRootOrHeader && preNode._color === 1 /* TreeNodeColor.RED */) {\n      preNode = preNode._right;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      // Must be root and left is null\n      if (isRootOrHeader) {\n        return preNode._parent;\n      }\n      let pre = preNode._parent;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  }\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  _next() {\n    let nextNode = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      let pre = nextNode._parent;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  }\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const PP = this._parent;\n    const V = this._right;\n    const R = V._left;\n    if (PP._parent === this) PP._parent = V;else if (PP._left === this) PP._left = V;else PP._right = V;\n    V._parent = PP;\n    V._left = this;\n    this._parent = V;\n    this._right = R;\n    if (R) R._parent = this;\n    return V;\n  }\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const PP = this._parent;\n    const F = this._left;\n    const K = F._right;\n    if (PP._parent === this) PP._parent = F;else if (PP._left === this) PP._left = F;else PP._right = F;\n    F._parent = PP;\n    F._right = this;\n    this._parent = F;\n    this._left = K;\n    if (K) K._parent = this;\n    return F;\n  }\n}\nclass TreeNodeEnableIndex extends TreeNode {\n  constructor() {\n    super(...arguments);\n    this._subTreeSize = 1;\n  }\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const parent = super._rotateLeft();\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const parent = super._rotateRight();\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  _recount() {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += this._left._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += this._right._subTreeSize;\n    }\n  }\n}\n\nclass ContainerIterator {\n  /**\n   * @internal\n   */\n  constructor(iteratorType = 0 /* IteratorType.NORMAL */) {\n    this.iteratorType = iteratorType;\n  }\n  /**\n   * @param iter - The other iterator you want to compare.\n   * @returns Whether this equals to obj.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  equals(iter) {\n    return this._node === iter._node;\n  }\n}\nclass Base {\n  constructor() {\n    /**\n     * @description Container's size.\n     * @internal\n     */\n    this._length = 0;\n  }\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.length); // 2\n   */\n  get length() {\n    return this._length;\n  }\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.size()); // 2\n   */\n  size() {\n    return this._length;\n  }\n  /**\n   * @returns Whether the container is empty.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  empty() {\n    return this._length === 0;\n  }\n}\nclass Container extends Base {}\n\n/**\n * @description Throw an iterator access error.\n * @internal\n */\nfunction throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n\nclass TreeContainer extends Container {\n  /**\n   * @internal\n   */\n  constructor(cmp = function (x, y) {\n    if (x < y) return -1;\n    if (x > y) return 1;\n    return 0;\n  }, enableIndex = false) {\n    super();\n    /**\n     * @internal\n     */\n    this._root = undefined;\n    this._cmp = cmp;\n    this.enableIndex = enableIndex;\n    this._TreeNodeClass = enableIndex ? TreeNodeEnableIndex : TreeNode;\n    this._header = new this._TreeNodeClass();\n  }\n  /**\n   * @internal\n   */\n  _lowerBound(curNode, key) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  _upperBound(curNode, key) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  _reverseLowerBound(curNode, key) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  _reverseUpperBound(curNode, key) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  _eraseNodeSelfBalance(curNode) {\n    while (true) {\n      const parentNode = curNode._parent;\n      if (parentNode === this._header) return;\n      if (curNode._color === 1 /* TreeNodeColor.RED */) {\n        curNode._color = 0 /* TreeNodeColor.BLACK */;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        const brother = parentNode._right;\n        if (brother._color === 1 /* TreeNodeColor.RED */) {\n          brother._color = 0 /* TreeNodeColor.BLACK */;\n          parentNode._color = 1 /* TreeNodeColor.RED */;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = parentNode._color;\n            parentNode._color = 0 /* TreeNodeColor.BLACK */;\n            brother._right._color = 0 /* TreeNodeColor.BLACK */;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            brother._left._color = 0 /* TreeNodeColor.BLACK */;\n            brother._rotateRight();\n          } else {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        const brother = parentNode._left;\n        if (brother._color === 1 /* TreeNodeColor.RED */) {\n          brother._color = 0 /* TreeNodeColor.BLACK */;\n          parentNode._color = 1 /* TreeNodeColor.RED */;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = parentNode._color;\n            parentNode._color = 0 /* TreeNodeColor.BLACK */;\n            brother._left._color = 0 /* TreeNodeColor.BLACK */;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            brother._right._color = 0 /* TreeNodeColor.BLACK */;\n            brother._rotateLeft();\n          } else {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  _eraseNode(curNode) {\n    if (this._length === 1) {\n      this.clear();\n      return;\n    }\n    let swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left;\n      }\n      const key = curNode._key;\n      curNode._key = swapNode._key;\n      swapNode._key = key;\n      const value = curNode._value;\n      curNode._value = swapNode._value;\n      swapNode._value = value;\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    let _parent = swapNode._parent;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root._color = 0 /* TreeNodeColor.BLACK */;\n    if (this.enableIndex) {\n      while (_parent !== this._header) {\n        _parent._subTreeSize -= 1;\n        _parent = _parent._parent;\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  _inOrderTraversal(param) {\n    const pos = typeof param === 'number' ? param : undefined;\n    const callback = typeof param === 'function' ? param : undefined;\n    const nodeList = typeof param === 'undefined' ? [] : undefined;\n    let index = 0;\n    let curNode = this._root;\n    const stack = [];\n    while (stack.length || curNode) {\n      if (curNode) {\n        stack.push(curNode);\n        curNode = curNode._left;\n      } else {\n        curNode = stack.pop();\n        if (index === pos) return curNode;\n        nodeList && nodeList.push(curNode);\n        callback && callback(curNode, index, this);\n        index += 1;\n        curNode = curNode._right;\n      }\n    }\n    return nodeList;\n  }\n  /**\n   * @internal\n   */\n  _insertNodeSelfBalance(curNode) {\n    while (true) {\n      const parentNode = curNode._parent;\n      if (parentNode._color === 0 /* TreeNodeColor.BLACK */) return;\n      const grandParent = parentNode._parent;\n      if (parentNode === grandParent._left) {\n        const uncle = grandParent._right;\n        if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n          uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) return;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (curNode._left) {\n            curNode._left._parent = parentNode;\n          }\n          if (curNode._right) {\n            curNode._right._parent = grandParent;\n          }\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n        } else {\n          parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          return;\n        }\n      } else {\n        const uncle = grandParent._left;\n        if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n          uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) return;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (curNode._left) {\n            curNode._left._parent = grandParent;\n          }\n          if (curNode._right) {\n            curNode._right._parent = parentNode;\n          }\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n        } else {\n          parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          return;\n        }\n      }\n      if (this.enableIndex) {\n        parentNode._recount();\n        grandParent._recount();\n        curNode._recount();\n      }\n      return;\n    }\n  }\n  /**\n   * @internal\n   */\n  _set(key, value, hint) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value, 0 /* TreeNodeColor.BLACK */);\n      this._root._parent = this._header;\n      this._header._parent = this._header._left = this._header._right = this._root;\n      return this._length;\n    }\n    let curNode;\n    const minNode = this._header._left;\n    const compareToMin = this._cmp(minNode._key, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return this._length;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      const maxNode = this._header._right;\n      const compareToMax = this._cmp(maxNode._key, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return this._length;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          const iterNode = hint._node;\n          if (iterNode !== this._header) {\n            const iterCmpRes = this._cmp(iterNode._key, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return this._length;\n            } else /* istanbul ignore else */if (iterCmpRes > 0) {\n                const preNode = iterNode._pre();\n                const preCmpRes = this._cmp(preNode._key, key);\n                if (preCmpRes === 0) {\n                  preNode._value = value;\n                  return this._length;\n                } else if (preCmpRes < 0) {\n                  curNode = new this._TreeNodeClass(key, value);\n                  if (preNode._right === undefined) {\n                    preNode._right = curNode;\n                    curNode._parent = preNode;\n                  } else {\n                    iterNode._left = curNode;\n                    curNode._parent = iterNode;\n                  }\n                }\n              }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return this._length;\n            }\n          }\n        }\n      }\n    }\n    if (this.enableIndex) {\n      let parent = curNode._parent;\n      while (parent !== this._header) {\n        parent._subTreeSize += 1;\n        parent = parent._parent;\n      }\n    }\n    this._insertNodeSelfBalance(curNode);\n    this._length += 1;\n    return this._length;\n  }\n  /**\n   * @internal\n   */\n  _getTreeNodeByKey(curNode, key) {\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  }\n  clear() {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  }\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  updateKeyByIterator(iter, key) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    const nextKey = node._next()._key;\n    if (node === this._header._left) {\n      if (this._cmp(nextKey, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    const preKey = node._pre()._key;\n    if (node === this._header._right) {\n      if (this._cmp(preKey, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (this._cmp(preKey, key) >= 0 || this._cmp(nextKey, key) <= 0) return false;\n    node._key = key;\n    return true;\n  }\n  eraseElementByPos(pos) {\n    if (pos < 0 || pos > this._length - 1) {\n      throw new RangeError();\n    }\n    const node = this._inOrderTraversal(pos);\n    this._eraseNode(node);\n    return this._length;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key) {\n    if (this._length === 0) return false;\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  }\n  eraseElementByIterator(iter) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    const hasNoRight = node._right === undefined;\n    const isNormal = iter.iteratorType === 0 /* IteratorType.NORMAL */;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  }\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  getHeight() {\n    if (this._length === 0) return 0;\n    function traversal(curNode) {\n      if (!curNode) return 0;\n      return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n    }\n    return traversal(this._root);\n  }\n}\n\nclass TreeIterator extends ContainerIterator {\n  /**\n   * @internal\n   */\n  constructor(node, header, iteratorType) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === 0 /* IteratorType.NORMAL */) {\n      this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n  }\n  /**\n   * @description Get the sequential index of the iterator in the tree container.<br/>\n   *              <strong>Note:</strong>\n   *              This function only takes effect when the specified tree container `enableIndex = true`.\n   * @returns The index subscript of the node in the tree.\n   * @example\n   * const st = new OrderedSet([1, 2, 3], true);\n   * console.log(st.begin().next().index);  // 1\n   */\n  get index() {\n    let _node = this._node;\n    const root = this._header._parent;\n    if (_node === this._header) {\n      if (root) {\n        return root._subTreeSize - 1;\n      }\n      return 0;\n    }\n    let index = 0;\n    if (_node._left) {\n      index += _node._left._subTreeSize;\n    }\n    while (_node !== root) {\n      const _parent = _node._parent;\n      if (_node === _parent._right) {\n        index += 1;\n        if (_parent._left) {\n          index += _parent._left._subTreeSize;\n        }\n      }\n      _node = _parent;\n    }\n    return index;\n  }\n  isAccessible() {\n    return this._node !== this._header;\n  }\n}\n\nclass OrderedMapIterator extends TreeIterator {\n  constructor(node, header, container, iteratorType) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy([], {\n      get(target, prop) {\n        if (prop === '0') return self._node._key;else if (prop === '1') return self._node._value;\n        target[0] = self._node._key;\n        target[1] = self._node._value;\n        return target[prop];\n      },\n      set(_, prop, newValue) {\n        if (prop !== '1') {\n          throw new TypeError('prop must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new OrderedMapIterator(this._node, this._header, this.container, this.iteratorType);\n  }\n}\nclass OrderedMap extends TreeContainer {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  constructor(container = [], cmp, enableIndex) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  begin() {\n    return new OrderedMapIterator(this._header._left || this._header, this._header, this);\n  }\n  end() {\n    return new OrderedMapIterator(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedMapIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n  }\n\n  rEnd() {\n    return new OrderedMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n  }\n\n  front() {\n    if (this._length === 0) return;\n    const minNode = this._header._left;\n    return [minNode._key, minNode._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    const maxNode = this._header._right;\n    return [maxNode._key, maxNode._value];\n  }\n  lowerBound(key) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  }\n  upperBound(key) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  }\n  reverseLowerBound(key) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  }\n  reverseUpperBound(key) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  }\n  forEach(callback) {\n    this._inOrderTraversal(function (node, index, map) {\n      callback([node._key, node._value], index, map);\n    });\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  setElement(key, value, hint) {\n    return this._set(key, value, hint);\n  }\n  getElementByPos(pos) {\n    if (pos < 0 || pos > this._length - 1) {\n      throw new RangeError();\n    }\n    const node = this._inOrderTraversal(pos);\n    return [node._key, node._value];\n  }\n  find(key) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return new OrderedMapIterator(curNode, this._header, this);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return curNode._value;\n  }\n  union(other) {\n    const self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  }\n  *[Symbol.iterator]() {\n    const length = this._length;\n    const nodeList = this._inOrderTraversal();\n    for (let i = 0; i < length; ++i) {\n      const node = nodeList[i];\n      yield [node._key, node._value];\n    }\n  }\n}\n\nexports.OrderedMap = OrderedMap;\n//# sourceMappingURL=index.js.map\n", "export const enum TreeNodeColor {\n  RED = 1,\n  BLACK = 0\n}\n\nexport class TreeNode<K, V> {\n  _color: TreeNodeColor;\n  _key: K | undefined;\n  _value: V | undefined;\n  _left: TreeNode<K, V> | undefined = undefined;\n  _right: TreeNode<K, V> | undefined = undefined;\n  _parent: TreeNode<K, V> | undefined = undefined;\n  constructor(\n    key?: K,\n    value?: V,\n    color: TreeNodeColor = TreeNodeColor.RED\n  ) {\n    this._key = key;\n    this._value = value;\n    this._color = color;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  _pre() {\n    let preNode: TreeNode<K, V> = this;\n    const isRootOrHeader = preNode._parent!._parent === preNode;\n    if (isRootOrHeader && preNode._color === TreeNodeColor.RED) {\n      preNode = preNode._right!;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      // Must be root and left is null\n      if (isRootOrHeader) {\n        return preNode._parent!;\n      }\n      let pre = preNode._parent!;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent!;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  }\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  _next() {\n    let nextNode: TreeNode<K, V> = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      let pre = nextNode._parent!;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent!;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  }\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const PP = this._parent!;\n    const V = this._right!;\n    const R = V._left;\n\n    if (PP._parent === this) PP._parent = V;\n    else if (PP._left === this) PP._left = V;\n    else PP._right = V;\n\n    V._parent = PP;\n    V._left = this;\n\n    this._parent = V;\n    this._right = R;\n\n    if (R) R._parent = this;\n\n    return V;\n  }\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const PP = this._parent!;\n    const F = this._left!;\n    const K = F._right;\n\n    if (PP._parent === this) PP._parent = F;\n    else if (PP._left === this) PP._left = F;\n    else PP._right = F;\n\n    F._parent = PP;\n    F._right = this;\n\n    this._parent = F;\n    this._left = K;\n\n    if (K) K._parent = this;\n\n    return F;\n  }\n}\n\nexport class TreeNodeEnableIndex<K, V> extends TreeNode<K, V> {\n  _subTreeSize = 1;\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const parent = super._rotateLeft() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const parent = super._rotateRight() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  _recount() {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += (this._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += (this._right as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n  }\n}\n", "/**\n * @description The iterator type including `NORMAL` and `REVERSE`.\n */\nexport const enum IteratorType {\n  NORMAL = 0,\n  REVERSE = 1\n}\n\nexport abstract class ContainerIterator<T> {\n  /**\n   * @description The container pointed to by the iterator.\n   */\n  abstract readonly container: Container<T>;\n  /**\n   * @internal\n   */\n  abstract _node: unknown;\n  /**\n   * @description Iterator's type.\n   * @example\n   * console.log(container.end().iteratorType === IteratorType.NORMAL);  // true\n   */\n  readonly iteratorType: IteratorType;\n  /**\n   * @internal\n   */\n  protected constructor(iteratorType = IteratorType.NORMAL) {\n    this.iteratorType = iteratorType;\n  }\n  /**\n   * @param iter - The other iterator you want to compare.\n   * @returns Whether this equals to obj.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  equals(iter: ContainerIterator<T>) {\n    return this._node === iter._node;\n  }\n  /**\n   * @description Pointers to element.\n   * @returns The value of the pointer's element.\n   * @example\n   * const val = container.begin().pointer;\n   */\n  abstract get pointer(): T;\n  /**\n   * @description Set pointer's value (some containers are unavailable).\n   * @param newValue - The new value you want to set.\n   * @example\n   * (<LinkList<number>>container).begin().pointer = 1;\n   */\n  abstract set pointer(newValue: T);\n  /**\n   * @description Move `this` iterator to pre.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [0, 1]\n   * const pre = iter.pre();\n   * console.log(pre === iter);  // true\n   * console.log(pre.equals(iter));  // true\n   * console.log(pre.pointer, iter.pointer); // 0, 0\n   */\n  abstract pre(): this;\n  /**\n   * @description Move `this` iterator to next.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.next();\n   * console.log(next === iter);  // true\n   * console.log(next.equals(iter));  // true\n   * console.log(next.pointer, iter.pointer); // 2, 2\n   */\n  abstract next(): this;\n  /**\n   * @description Get a copy of itself.\n   * @returns The copy of self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.copy().next();\n   * console.log(next === iter);  // false\n   * console.log(next.equals(iter));  // false\n   * console.log(next.pointer, iter.pointer); // 2, 1\n   */\n  abstract copy(): ContainerIterator<T>;\n  abstract isAccessible(): boolean;\n}\n\nexport abstract class Base {\n  /**\n   * @description Container's size.\n   * @internal\n   */\n  protected _length = 0;\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.length); // 2\n   */\n  get length() {\n    return this._length;\n  }\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.size()); // 2\n   */\n  size() {\n    return this._length;\n  }\n  /**\n   * @returns Whether the container is empty.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  empty() {\n    return this._length === 0;\n  }\n  /**\n   * @description Clear the container.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  abstract clear(): void;\n}\n\nexport abstract class Container<T> extends Base {\n  /**\n   * @returns Iterator pointing to the beginning element.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract begin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super end like c++.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract end(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the end element.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rBegin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super begin like c++.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rEnd(): ContainerIterator<T>;\n  /**\n   * @returns The first element of the container.\n   */\n  abstract front(): T | undefined;\n  /**\n   * @returns The last element of the container.\n   */\n  abstract back(): T | undefined;\n  /**\n   * @param element - The element you want to find.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  abstract find(element: T): ContainerIterator<T>;\n  /**\n   * @description Iterate over all elements in the container.\n   * @param callback - Callback function like Array.forEach.\n   * @example\n   * container.forEach((element, index) => console.log(element, index));\n   */\n  abstract forEach(callback: (element: T, index: number, container: Container<T>) => void): void;\n  /**\n   * @description Gets the value of the element at the specified position.\n   * @example\n   * const val = container.getElementByPos(-1); // throw a RangeError\n   */\n  abstract getElementByPos(pos: number): T;\n  /**\n   * @description Removes the element at the specified position.\n   * @param pos - The element's position you want to remove.\n   * @returns The container length after erasing.\n   * @example\n   * container.eraseElementByPos(-1); // throw a RangeError\n   */\n  abstract eraseElementByPos(pos: number): number;\n  /**\n   * @description Removes element by iterator and move `iter` to next.\n   * @param iter - The iterator you want to erase.\n   * @returns The next iterator.\n   * @example\n   * container.eraseElementByIterator(container.begin());\n   * container.eraseElementByIterator(container.end()); // throw a RangeError\n   */\n  abstract eraseElementByIterator(\n    iter: ContainerIterator<T>\n  ): ContainerIterator<T>;\n  /**\n   * @description Using for `for...of` syntax like Array.\n   * @example\n   * for (const element of container) {\n   *   console.log(element);\n   * }\n   */\n  abstract [Symbol.iterator](): Generator<T, void>;\n}\n\n/**\n * @description The initial data type passed in when initializing the container.\n */\nexport type initContainer<T> = {\n  size?: number | (() => number);\n  length?: number;\n  forEach: (callback: (el: T) => void) => void;\n}\n", "/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n", "import type TreeIterator from './TreeIterator';\nimport { TreeNode, TreeNodeColor, TreeNodeEnableIndex } from './TreeNode';\nimport { Container, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeContainer<K, V> extends Container<K | [K, V]> {\n  enableIndex: boolean;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _root: TreeNode<K, V> | undefined = undefined;\n  /**\n   * @internal\n   */\n  protected readonly _cmp: (x: K, y: K) => number;\n  /**\n   * @internal\n   */\n  protected readonly _TreeNodeClass: typeof TreeNode | typeof TreeNodeEnableIndex;\n  /**\n   * @internal\n   */\n  protected constructor(\n    cmp: (x: K, y: K) => number =\n    function (x: K, y: K) {\n      if (x < y) return -1;\n      if (x > y) return 1;\n      return 0;\n    },\n    enableIndex = false\n  ) {\n    super();\n    this._cmp = cmp;\n    this.enableIndex = enableIndex;\n    this._TreeNodeClass = enableIndex ? TreeNodeEnableIndex : TreeNode;\n    this._header = new this._TreeNodeClass();\n  }\n  /**\n   * @internal\n   */\n  protected _lowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _upperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseLowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseUpperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode === this._header) return;\n      if (curNode._color === TreeNodeColor.RED) {\n        curNode._color = TreeNodeColor.BLACK;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        const brother = parentNode._right!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._right._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._left._color = TreeNodeColor.BLACK;\n            brother._rotateRight();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        const brother = parentNode._left!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._left._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._right._color = TreeNodeColor.BLACK;\n            brother._rotateLeft();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNode(curNode: TreeNode<K, V>) {\n    if (this._length === 1) {\n      this.clear();\n      return;\n    }\n    let swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left!;\n      }\n      const key = curNode._key;\n      curNode._key = swapNode._key;\n      swapNode._key = key;\n      const value = curNode._value;\n      curNode._value = swapNode._value;\n      swapNode._value = value;\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    let _parent = swapNode._parent as TreeNodeEnableIndex<K, V>;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root!._color = TreeNodeColor.BLACK;\n    if (this.enableIndex) {\n      while (_parent !== this._header) {\n        _parent._subTreeSize -= 1;\n        _parent = _parent._parent as TreeNodeEnableIndex<K, V>;\n      }\n    }\n  }\n  protected _inOrderTraversal(): TreeNode<K, V>[];\n  protected _inOrderTraversal(pos: number): TreeNode<K, V>;\n  protected _inOrderTraversal(\n    callback: (node: TreeNode<K, V>, index: number, map: this) => void\n  ): TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _inOrderTraversal(\n    param?: number | ((node: TreeNode<K, V>, index: number, map: this) => void)\n  ) {\n    const pos = typeof param === 'number' ? param : undefined;\n    const callback = typeof param === 'function' ? param : undefined;\n    const nodeList = typeof param === 'undefined' ? <TreeNode<K, V>[]>[] : undefined;\n    let index = 0;\n    let curNode = this._root;\n    const stack: TreeNode<K, V>[] = [];\n    while (stack.length || curNode) {\n      if (curNode) {\n        stack.push(curNode);\n        curNode = curNode._left;\n      } else {\n        curNode = stack.pop()!;\n        if (index === pos) return curNode;\n        nodeList && nodeList.push(curNode);\n        callback && callback(curNode, index, this);\n        index += 1;\n        curNode = curNode._right;\n      }\n    }\n    return nodeList;\n  }\n  /**\n   * @internal\n   */\n  protected _insertNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode._color === TreeNodeColor.BLACK) return;\n      const grandParent = parentNode._parent!;\n      if (parentNode === grandParent._left) {\n        const uncle = grandParent._right;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) {\n            curNode._left._parent = parentNode;\n          }\n          if (curNode._right) {\n            curNode._right._parent = grandParent;\n          }\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = TreeNodeColor.RED;\n          return;\n        }\n      } else {\n        const uncle = grandParent._left;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) {\n            curNode._left._parent = grandParent;\n          }\n          if (curNode._right) {\n            curNode._right._parent = parentNode;\n          }\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = TreeNodeColor.RED;\n          return;\n        }\n      }\n      if (this.enableIndex) {\n        (<TreeNodeEnableIndex<K, V>>parentNode)._recount();\n        (<TreeNodeEnableIndex<K, V>>grandParent)._recount();\n        (<TreeNodeEnableIndex<K, V>>curNode)._recount();\n      }\n      return;\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _set(key: K, value?: V, hint?: TreeIterator<K, V>) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value, TreeNodeColor.BLACK);\n      this._root._parent = this._header;\n      this._header._parent = this._header._left = this._header._right = this._root;\n      return this._length;\n    }\n    let curNode;\n    const minNode = this._header._left!;\n    const compareToMin = this._cmp(minNode._key!, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return this._length;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      const maxNode = this._header._right!;\n      const compareToMax = this._cmp(maxNode._key!, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return this._length;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          const iterNode = hint._node;\n          if (iterNode !== this._header) {\n            const iterCmpRes = this._cmp(iterNode._key!, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return this._length;\n            } else /* istanbul ignore else */ if (iterCmpRes > 0) {\n              const preNode = iterNode._pre();\n              const preCmpRes = this._cmp(preNode._key!, key);\n              if (preCmpRes === 0) {\n                preNode._value = value;\n                return this._length;\n              } else if (preCmpRes < 0) {\n                curNode = new this._TreeNodeClass(key, value);\n                if (preNode._right === undefined) {\n                  preNode._right = curNode;\n                  curNode._parent = preNode;\n                } else {\n                  iterNode._left = curNode;\n                  curNode._parent = iterNode;\n                }\n              }\n            }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            const cmpResult = this._cmp(curNode._key!, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return this._length;\n            }\n          }\n        }\n      }\n    }\n    if (this.enableIndex) {\n      let parent = curNode._parent as TreeNodeEnableIndex<K, V>;\n      while (parent !== this._header) {\n        parent._subTreeSize += 1;\n        parent = parent._parent as TreeNodeEnableIndex<K, V>;\n      }\n    }\n    this._insertNodeSelfBalance(curNode);\n    this._length += 1;\n    return this._length;\n  }\n  /**\n   * @internal\n   */\n  protected _getTreeNodeByKey(curNode: TreeNode<K, V> | undefined, key: K) {\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  }\n  clear() {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  }\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  updateKeyByIterator(iter: TreeIterator<K, V>, key: K): boolean {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    const nextKey = node._next()._key!;\n    if (node === this._header._left) {\n      if (this._cmp(nextKey, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    const preKey = node._pre()._key!;\n    if (node === this._header._right) {\n      if (this._cmp(preKey, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (\n      this._cmp(preKey, key) >= 0 ||\n      this._cmp(nextKey, key) <= 0\n    ) return false;\n    node._key = key;\n    return true;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const node = this._inOrderTraversal(pos);\n    this._eraseNode(node);\n    return this._length;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K) {\n    if (this._length === 0) return false;\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  }\n  eraseElementByIterator(iter: TreeIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    const hasNoRight = node._right === undefined;\n    const isNormal = iter.iteratorType === IteratorType.NORMAL;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  }\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  getHeight() {\n    if (this._length === 0) return 0;\n    function traversal(curNode: TreeNode<K, V> | undefined): number {\n      if (!curNode) return 0;\n      return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n    }\n    return traversal(this._root);\n  }\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element less than the given key.\n   */\n  abstract reverseUpperBound(key: K): TreeIterator<K, V>;\n  /**\n   * @description Union the other tree to self.\n   * @param other - The other tree container you want to merge.\n   * @returns The size of the tree after union.\n   */\n  abstract union(other: TreeContainer<K, V>): number;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not greater than the given key.\n   */\n  abstract reverseLowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not less than the given key.\n   */\n  abstract lowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element greater than the given key.\n   */\n  abstract upperBound(key: K): TreeIterator<K, V>;\n}\n\nexport default TreeContainer;\n", "import { TreeNode } from './TreeNode';\nimport type { TreeNodeEnableIndex } from './TreeNode';\nimport { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport TreeContainer from '@/container/TreeContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: TreeContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n  }\n  /**\n   * @description Get the sequential index of the iterator in the tree container.<br/>\n   *              <strong>Note:</strong>\n   *              This function only takes effect when the specified tree container `enableIndex = true`.\n   * @returns The index subscript of the node in the tree.\n   * @example\n   * const st = new OrderedSet([1, 2, 3], true);\n   * console.log(st.begin().next().index);  // 1\n   */\n  get index() {\n    let _node = this._node as TreeNodeEnableIndex<K, V>;\n    const root = this._header._parent as TreeNodeEnableIndex<K, V>;\n    if (_node === this._header) {\n      if (root) {\n        return root._subTreeSize - 1;\n      }\n      return 0;\n    }\n    let index = 0;\n    if (_node._left) {\n      index += (_node._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    while (_node !== root) {\n      const _parent = _node._parent as TreeNodeEnableIndex<K, V>;\n      if (_node === _parent._right) {\n        index += 1;\n        if (_parent._left) {\n          index += (_parent._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n        }\n      }\n      _node = _parent;\n    }\n    return index;\n  }\n  isAccessible() {\n    return this._node !== this._header;\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport default TreeIterator;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedMapIterator<K, V> extends TreeIterator<K, V> {\n  container: OrderedMap<K, V>;\n  constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    container: OrderedMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(target, prop: '0' | '1') {\n        if (prop === '0') return self._node._key!;\n        else if (prop === '1') return self._node._value!;\n        target[0] = self._node._key!;\n        target[1] = self._node._value!;\n        return target[prop];\n      },\n      set(_, prop: '1', newValue: V) {\n        if (prop !== '1') {\n          throw new TypeError('prop must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new OrderedMapIterator<K, V>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedMapIterator<K, V>): boolean;\n}\n\nexport type { OrderedMapIterator };\n\nclass OrderedMap<K, V> extends TreeContainer<K, V> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<[K, V]> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  begin() {\n    return new OrderedMapIterator<K, V>(this._header._left || this._header, this._header, this);\n  }\n  end() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedMapIterator<K, V>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    const minNode = this._header._left!;\n    return <[K, V]>[minNode._key, minNode._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    const maxNode = this._header._right!;\n    return <[K, V]>[maxNode._key, maxNode._value];\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  forEach(callback: (element: [K, V], index: number, map: OrderedMap<K, V>) => void) {\n    this._inOrderTraversal(function (node, index, map) {\n      callback(<[K, V]>[node._key, node._value], index, map);\n    });\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  setElement(key: K, value: V, hint?: OrderedMapIterator<K, V>) {\n    return this._set(key, value, hint);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const node = this._inOrderTraversal(pos);\n    return <[K, V]>[node._key, node._value];\n  }\n  find(key: K) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return new OrderedMapIterator<K, V>(curNode, this._header, this);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return curNode._value;\n  }\n  union(other: OrderedMap<K, V>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  }\n  * [Symbol.iterator]() {\n    const length = this._length;\n    const nodeList = this._inOrderTraversal();\n    for (let i = 0; i < length; ++i) {\n      const node = nodeList[i];\n      yield <[K, V]>[node._key, node._value];\n    }\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedMapIterator<K, V>): OrderedMapIterator<K, V>;\n}\n\nexport default OrderedMap;\n"]}