{"name": "available-typed-arrays", "version": "1.0.7", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/array.prototype.every": "^1.1.1", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.4", "array.prototype.every": "^1.1.5", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "isarray": "^2.0.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "^5.4.0-dev.20240131"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "dependencies": {"possible-typed-array-names": "^1.0.0"}}