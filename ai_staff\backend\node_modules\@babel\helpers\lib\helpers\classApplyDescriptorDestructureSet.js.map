{"version": 3, "names": ["_classApplyDescriptorDestructureSet", "receiver", "descriptor", "set", "__destr<PERSON>bj", "value", "v", "call", "writable", "TypeError"], "sources": ["../../src/helpers/classApplyDescriptorDestructureSet.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nexport default function _classApplyDescriptorDestructureSet(\n  receiver,\n  descriptor,\n) {\n  if (descriptor.set) {\n    if (!(\"__destrObj\" in descriptor)) {\n      descriptor.__destrObj = {\n        set value(v) {\n          descriptor.set.call(receiver, v);\n        },\n      };\n    }\n    return descriptor.__destrObj;\n  } else {\n    if (!descriptor.writable) {\n      // This should only throw in strict mode, but class bodies are\n      // always strict and private fields can only be used inside\n      // class bodies.\n      throw new TypeError(\"attempted to set read only private field\");\n    }\n\n    return descriptor;\n  }\n}\n"], "mappings": ";;;;;;AAGe,SAASA,mCAAmCA,CACzDC,QAAQ,EACRC,UAAU,EACV;EACA,IAAIA,UAAU,CAACC,GAAG,EAAE;IAClB,IAAI,EAAE,YAAY,IAAID,UAAU,CAAC,EAAE;MACjCA,UAAU,CAACE,UAAU,GAAG;QACtB,IAAIC,KAAKA,CAACC,CAAC,EAAE;UACXJ,UAAU,CAACC,GAAG,CAACI,IAAI,CAACN,QAAQ,EAAEK,CAAC,CAAC;QAClC;MACF,CAAC;IACH;IACA,OAAOJ,UAAU,CAACE,UAAU;EAC9B,CAAC,MAAM;IACL,IAAI,CAACF,UAAU,CAACM,QAAQ,EAAE;MAIxB,MAAM,IAAIC,SAAS,CAAC,0CAA0C,CAAC;IACjE;IAEA,OAAOP,UAAU;EACnB;AACF", "ignoreList": []}