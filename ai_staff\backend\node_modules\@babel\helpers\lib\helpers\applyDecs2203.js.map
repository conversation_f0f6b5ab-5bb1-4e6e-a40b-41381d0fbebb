{"version": 3, "names": ["applyDecs2203Factory", "createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "kindStr", "ctx", "static", "private", "v", "get", "set", "call", "access", "fnName", "Error", "fn", "hint", "TypeError", "assertValidReturnValue", "type", "undefined", "init", "applyMemberDec", "ret", "base", "decInfo", "decs", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2203Impl", "memberDecs", "applyDecs2203"], "sources": ["../../src/helpers/applyDecs2203.js"], "sourcesContent": ["/* @minVersion 7.19.0 */\n/* @onlyBabel7 */\n\n/**\n * NOTE: This is an old version of the helper, used for 2022-03 decorators.\n * Updates should be done in applyDecs2203R.js.\n */\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\nfunction applyDecs2203Factory() {\n  function createAddInitializerMethod(initializers, decoratorFinishedRef) {\n    return function addInitializer(initializer) {\n      assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n      assertCallable(initializer, \"An initializer\");\n      initializers.push(initializer);\n    };\n  }\n\n  function memberDec(\n    dec,\n    name,\n    desc,\n    initializers,\n    kind,\n    isStatic,\n    isPrivate,\n    value,\n  ) {\n    var kindStr;\n\n    switch (kind) {\n      case 1 /* ACCESSOR */:\n        kindStr = \"accessor\";\n        break;\n      case 2 /* METHOD */:\n        kindStr = \"method\";\n        break;\n      case 3 /* GETTER */:\n        kindStr = \"getter\";\n        break;\n      case 4 /* SETTER */:\n        kindStr = \"setter\";\n        break;\n      default:\n        kindStr = \"field\";\n    }\n\n    var ctx = {\n      kind: kindStr,\n      name: isPrivate ? \"#\" + name : name,\n      static: isStatic,\n      private: isPrivate,\n    };\n\n    var decoratorFinishedRef = { v: false };\n\n    if (kind !== 0 /* FIELD */) {\n      ctx.addInitializer = createAddInitializerMethod(\n        initializers,\n        decoratorFinishedRef,\n      );\n    }\n\n    var get, set;\n    if (kind === 0 /* FIELD */) {\n      if (isPrivate) {\n        get = desc.get;\n        set = desc.set;\n      } else {\n        get = function () {\n          return this[name];\n        };\n        set = function (v) {\n          this[name] = v;\n        };\n      }\n    } else if (kind === 2 /* METHOD */) {\n      get = function () {\n        return desc.value;\n      };\n    } else {\n      // replace with values that will go through the final getter and setter\n      if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n        get = function () {\n          return desc.get.call(this);\n        };\n      }\n\n      if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n        set = function (v) {\n          desc.set.call(this, v);\n        };\n      }\n    }\n    ctx.access =\n      get && set ? { get: get, set: set } : get ? { get: get } : { set: set };\n\n    try {\n      return dec(value, ctx);\n    } finally {\n      decoratorFinishedRef.v = true;\n    }\n  }\n\n  function assertNotFinished(decoratorFinishedRef, fnName) {\n    if (decoratorFinishedRef.v) {\n      throw new Error(\n        \"attempted to call \" + fnName + \" after decoration was finished\",\n      );\n    }\n  }\n\n  function assertCallable(fn, hint) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(hint + \" must be a function\");\n    }\n  }\n\n  function assertValidReturnValue(kind, value) {\n    var type = typeof value;\n\n    if (kind === 1 /* ACCESSOR */) {\n      if (type !== \"object\" || value === null) {\n        throw new TypeError(\n          \"accessor decorators must return an object with get, set, or init properties or void 0\",\n        );\n      }\n      if (value.get !== undefined) {\n        assertCallable(value.get, \"accessor.get\");\n      }\n      if (value.set !== undefined) {\n        assertCallable(value.set, \"accessor.set\");\n      }\n      if (value.init !== undefined) {\n        assertCallable(value.init, \"accessor.init\");\n      }\n    } else if (type !== \"function\") {\n      var hint;\n      if (kind === 0 /* FIELD */) {\n        hint = \"field\";\n      } else if (kind === 10 /* CLASS */) {\n        hint = \"class\";\n      } else {\n        hint = \"method\";\n      }\n      throw new TypeError(\n        hint + \" decorators must return a function or void 0\",\n      );\n    }\n  }\n\n  function applyMemberDec(\n    ret,\n    base,\n    decInfo,\n    name,\n    kind,\n    isStatic,\n    isPrivate,\n    initializers,\n  ) {\n    var decs = decInfo[0];\n\n    var desc, init, value;\n\n    if (isPrivate) {\n      if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n        desc = {\n          get: decInfo[3],\n          set: decInfo[4],\n        };\n      } else if (kind === 3 /* GETTER */) {\n        desc = {\n          get: decInfo[3],\n        };\n      } else if (kind === 4 /* SETTER */) {\n        desc = {\n          set: decInfo[3],\n        };\n      } else {\n        desc = {\n          value: decInfo[3],\n        };\n      }\n    } else if (kind !== 0 /* FIELD */) {\n      desc = Object.getOwnPropertyDescriptor(base, name);\n    }\n\n    if (kind === 1 /* ACCESSOR */) {\n      value = {\n        get: desc.get,\n        set: desc.set,\n      };\n    } else if (kind === 2 /* METHOD */) {\n      value = desc.value;\n    } else if (kind === 3 /* GETTER */) {\n      value = desc.get;\n    } else if (kind === 4 /* SETTER */) {\n      value = desc.set;\n    }\n\n    var newValue, get, set;\n\n    if (typeof decs === \"function\") {\n      newValue = memberDec(\n        decs,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n\n        if (kind === 0 /* FIELD */) {\n          init = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          init = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n      }\n    } else {\n      for (var i = decs.length - 1; i >= 0; i--) {\n        var dec = decs[i];\n\n        newValue = memberDec(\n          dec,\n          name,\n          desc,\n          initializers,\n          kind,\n          isStatic,\n          isPrivate,\n          value,\n        );\n\n        if (newValue !== void 0) {\n          assertValidReturnValue(kind, newValue);\n          var newInit;\n\n          if (kind === 0 /* FIELD */) {\n            newInit = newValue;\n          } else if (kind === 1 /* ACCESSOR */) {\n            newInit = newValue.init;\n            get = newValue.get || value.get;\n            set = newValue.set || value.set;\n\n            value = { get: get, set: set };\n          } else {\n            value = newValue;\n          }\n\n          if (newInit !== void 0) {\n            if (init === void 0) {\n              init = newInit;\n            } else if (typeof init === \"function\") {\n              init = [init, newInit];\n            } else {\n              init.push(newInit);\n            }\n          }\n        }\n      }\n    }\n\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      if (init === void 0) {\n        // If the initializer was void 0, sub in a dummy initializer\n        init = function (instance, init) {\n          return init;\n        };\n      } else if (typeof init !== \"function\") {\n        var ownInitializers = init;\n\n        init = function (instance, init) {\n          var value = init;\n\n          for (var i = 0; i < ownInitializers.length; i++) {\n            value = ownInitializers[i].call(instance, value);\n          }\n\n          return value;\n        };\n      } else {\n        var originalInitializer = init;\n\n        init = function (instance, init) {\n          return originalInitializer.call(instance, init);\n        };\n      }\n\n      ret.push(init);\n    }\n\n    if (kind !== 0 /* FIELD */) {\n      if (kind === 1 /* ACCESSOR */) {\n        desc.get = value.get;\n        desc.set = value.set;\n      } else if (kind === 2 /* METHOD */) {\n        desc.value = value;\n      } else if (kind === 3 /* GETTER */) {\n        desc.get = value;\n      } else if (kind === 4 /* SETTER */) {\n        desc.set = value;\n      }\n\n      if (isPrivate) {\n        if (kind === 1 /* ACCESSOR */) {\n          ret.push(function (instance, args) {\n            return value.get.call(instance, args);\n          });\n          ret.push(function (instance, args) {\n            return value.set.call(instance, args);\n          });\n        } else if (kind === 2 /* METHOD */) {\n          ret.push(value);\n        } else {\n          ret.push(function (instance, args) {\n            return value.call(instance, args);\n          });\n        }\n      } else {\n        Object.defineProperty(base, name, desc);\n      }\n    }\n  }\n\n  function applyMemberDecs(ret, Class, decInfos) {\n    var protoInitializers;\n    var staticInitializers;\n\n    var existingProtoNonFields = new Map();\n    var existingStaticNonFields = new Map();\n\n    for (var i = 0; i < decInfos.length; i++) {\n      var decInfo = decInfos[i];\n\n      // skip computed property names\n      if (!Array.isArray(decInfo)) continue;\n\n      var kind = decInfo[1];\n      var name = decInfo[2];\n      var isPrivate = decInfo.length > 3;\n\n      var isStatic = kind >= 5; /* STATIC */\n      var base;\n      var initializers;\n\n      if (isStatic) {\n        base = Class;\n        kind = kind - 5 /* STATIC */;\n        // initialize staticInitializers when we see a non-field static member\n        if (kind !== 0 /* FIELD */) {\n          staticInitializers = staticInitializers || [];\n          initializers = staticInitializers;\n        }\n      } else {\n        base = Class.prototype;\n        // initialize protoInitializers when we see a non-field member\n        if (kind !== 0 /* FIELD */) {\n          protoInitializers = protoInitializers || [];\n          initializers = protoInitializers;\n        }\n      }\n\n      if (kind !== 0 /* FIELD */ && !isPrivate) {\n        var existingNonFields = isStatic\n          ? existingStaticNonFields\n          : existingProtoNonFields;\n\n        var existingKind = existingNonFields.get(name) || 0;\n\n        if (\n          existingKind === true ||\n          (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n          (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n        ) {\n          throw new Error(\n            \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n              name,\n          );\n        } else if (!existingKind && kind > 2 /* METHOD */) {\n          existingNonFields.set(name, kind);\n        } else {\n          existingNonFields.set(name, true);\n        }\n      }\n\n      applyMemberDec(\n        ret,\n        base,\n        decInfo,\n        name,\n        kind,\n        isStatic,\n        isPrivate,\n        initializers,\n      );\n    }\n\n    pushInitializers(ret, protoInitializers);\n    pushInitializers(ret, staticInitializers);\n  }\n\n  function pushInitializers(ret, initializers) {\n    if (initializers) {\n      ret.push(function (instance) {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(instance);\n        }\n        return instance;\n      });\n    }\n  }\n\n  function applyClassDecs(ret, targetClass, classDecs) {\n    if (classDecs.length > 0) {\n      var initializers = [];\n      var newClass = targetClass;\n      var name = targetClass.name;\n\n      for (var i = classDecs.length - 1; i >= 0; i--) {\n        var decoratorFinishedRef = { v: false };\n\n        try {\n          var nextNewClass = classDecs[i](newClass, {\n            kind: \"class\",\n            name: name,\n            addInitializer: createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef,\n            ),\n          });\n        } finally {\n          decoratorFinishedRef.v = true;\n        }\n\n        if (nextNewClass !== undefined) {\n          assertValidReturnValue(10 /* CLASS */, nextNewClass);\n          newClass = nextNewClass;\n        }\n      }\n\n      ret.push(newClass, function () {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(newClass);\n        }\n      });\n    }\n  }\n\n  /**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\n  return function applyDecs2203Impl(targetClass, memberDecs, classDecs) {\n    var ret = [];\n    applyMemberDecs(ret, targetClass, memberDecs);\n    applyClassDecs(ret, targetClass, classDecs);\n    return ret;\n  };\n}\n\nvar applyDecs2203Impl;\n\nexport default function applyDecs2203(targetClass, memberDecs, classDecs) {\n  applyDecs2203Impl = applyDecs2203Impl || applyDecs2203Factory();\n  return applyDecs2203Impl(targetClass, memberDecs, classDecs);\n}\n"], "mappings": ";;;;;;AAyBA,SAASA,oBAAoBA,CAAA,EAAG;EAC9B,SAASC,0BAA0BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;IACtE,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAE;MAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;MACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;MAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;IAChC,CAAC;EACH;EAEA,SAASI,SAASA,CAChBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACL;IACA,IAAIC,OAAO;IAEX,QAAQJ,IAAI;MACV,KAAK,CAAC;QACJI,OAAO,GAAG,UAAU;QACpB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF;QACEA,OAAO,GAAG,OAAO;IACrB;IAEA,IAAIC,GAAG,GAAG;MACRL,IAAI,EAAEI,OAAO;MACbN,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGA,IAAI;MACnCQ,MAAM,EAAEL,QAAQ;MAChBM,OAAO,EAAEL;IACX,CAAC;IAED,IAAIZ,oBAAoB,GAAG;MAAEkB,CAAC,EAAE;IAAM,CAAC;IAEvC,IAAIR,IAAI,KAAK,CAAC,EAAc;MAC1BK,GAAG,CAACd,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBACF,CAAC;IACH;IAEA,IAAImB,GAAG,EAAEC,GAAG;IACZ,IAAIV,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIE,SAAS,EAAE;QACbO,GAAG,GAAGV,IAAI,CAACU,GAAG;QACdC,GAAG,GAAGX,IAAI,CAACW,GAAG;MAChB,CAAC,MAAM;QACLD,GAAG,GAAG,SAAAA,CAAA,EAAY;UAChB,OAAO,IAAI,CAACX,IAAI,CAAC;QACnB,CAAC;QACDY,GAAG,GAAG,SAAAA,CAAUF,CAAC,EAAE;UACjB,IAAI,CAACV,IAAI,CAAC,GAAGU,CAAC;QAChB,CAAC;MACH;IACF,CAAC,MAAM,IAAIR,IAAI,KAAK,CAAC,EAAe;MAClCS,GAAG,GAAG,SAAAA,CAAA,EAAY;QAChB,OAAOV,IAAI,CAACI,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIH,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxDS,GAAG,GAAG,SAAAA,CAAA,EAAY;UAChB,OAAOV,IAAI,CAACU,GAAG,CAACE,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;MACH;MAEA,IAAIX,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxDU,GAAG,GAAG,SAAAA,CAAUF,CAAC,EAAE;UACjBT,IAAI,CAACW,GAAG,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;QACxB,CAAC;MACH;IACF;IACAH,GAAG,CAACO,MAAM,GACRH,GAAG,IAAIC,GAAG,GAAG;MAAED,GAAG,EAAEA,GAAG;MAAEC,GAAG,EAAEA;IAAI,CAAC,GAAGD,GAAG,GAAG;MAAEA,GAAG,EAAEA;IAAI,CAAC,GAAG;MAAEC,GAAG,EAAEA;IAAI,CAAC;IAEzE,IAAI;MACF,OAAOb,GAAG,CAACM,KAAK,EAAEE,GAAG,CAAC;IACxB,CAAC,SAAS;MACRf,oBAAoB,CAACkB,CAAC,GAAG,IAAI;IAC/B;EACF;EAEA,SAASf,iBAAiBA,CAACH,oBAAoB,EAAEuB,MAAM,EAAE;IACvD,IAAIvB,oBAAoB,CAACkB,CAAC,EAAE;MAC1B,MAAM,IAAIM,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;IACH;EACF;EAEA,SAASnB,cAAcA,CAACqB,EAAE,EAAEC,IAAI,EAAE;IAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIE,SAAS,CAACD,IAAI,GAAG,qBAAqB,CAAC;IACnD;EACF;EAEA,SAASE,sBAAsBA,CAAClB,IAAI,EAAEG,KAAK,EAAE;IAC3C,IAAIgB,IAAI,GAAG,OAAOhB,KAAK;IAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;MAC7B,IAAImB,IAAI,KAAK,QAAQ,IAAIhB,KAAK,KAAK,IAAI,EAAE;QACvC,MAAM,IAAIc,SAAS,CACjB,uFACF,CAAC;MACH;MACA,IAAId,KAAK,CAACM,GAAG,KAAKW,SAAS,EAAE;QAC3B1B,cAAc,CAACS,KAAK,CAACM,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIN,KAAK,CAACO,GAAG,KAAKU,SAAS,EAAE;QAC3B1B,cAAc,CAACS,KAAK,CAACO,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIP,KAAK,CAACkB,IAAI,KAAKD,SAAS,EAAE;QAC5B1B,cAAc,CAACS,KAAK,CAACkB,IAAI,EAAE,eAAe,CAAC;MAC7C;IACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIH,IAAI;MACR,IAAIhB,IAAI,KAAK,CAAC,EAAc;QAC1BgB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAIhB,IAAI,KAAK,EAAE,EAAc;QAClCgB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACLA,IAAI,GAAG,QAAQ;MACjB;MACA,MAAM,IAAIC,SAAS,CACjBD,IAAI,GAAG,8CACT,CAAC;IACH;EACF;EAEA,SAASM,cAAcA,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP3B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTb,YAAY,EACZ;IACA,IAAIqC,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;IAErB,IAAI1B,IAAI,EAAEsB,IAAI,EAAElB,KAAK;IAErB,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;QACvDD,IAAI,GAAG;UACLU,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC;UACff,GAAG,EAAEe,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM,IAAIzB,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLU,GAAG,EAAEgB,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM,IAAIzB,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLW,GAAG,EAAEe,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM;QACL1B,IAAI,GAAG;UACLI,KAAK,EAAEsB,OAAO,CAAC,CAAC;QAClB,CAAC;MACH;IACF,CAAC,MAAM,IAAIzB,IAAI,KAAK,CAAC,EAAc;MACjCD,IAAI,GAAG4B,MAAM,CAACC,wBAAwB,CAACJ,IAAI,EAAE1B,IAAI,CAAC;IACpD;IAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;MAC7BG,KAAK,GAAG;QACNM,GAAG,EAAEV,IAAI,CAACU,GAAG;QACbC,GAAG,EAAEX,IAAI,CAACW;MACZ,CAAC;IACH,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACU,GAAG;IAClB,CAAC,MAAM,IAAIT,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAG;IAClB;IAEA,IAAImB,QAAQ,EAAEpB,GAAG,EAAEC,GAAG;IAEtB,IAAI,OAAOgB,IAAI,KAAK,UAAU,EAAE;MAC9BG,QAAQ,GAAGjC,SAAS,CAClB8B,IAAI,EACJ5B,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KACF,CAAC;MAED,IAAI0B,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBX,sBAAsB,CAAClB,IAAI,EAAE6B,QAAQ,CAAC;QAEtC,IAAI7B,IAAI,KAAK,CAAC,EAAc;UAC1BqB,IAAI,GAAGQ,QAAQ;QACjB,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAiB;UACpCqB,IAAI,GAAGQ,QAAQ,CAACR,IAAI;UACpBZ,GAAG,GAAGoB,QAAQ,CAACpB,GAAG,IAAIN,KAAK,CAACM,GAAG;UAC/BC,GAAG,GAAGmB,QAAQ,CAACnB,GAAG,IAAIP,KAAK,CAACO,GAAG;UAE/BP,KAAK,GAAG;YAAEM,GAAG,EAAEA,GAAG;YAAEC,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLP,KAAK,GAAG0B,QAAQ;QAClB;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAIjC,GAAG,GAAG6B,IAAI,CAACI,CAAC,CAAC;QAEjBD,QAAQ,GAAGjC,SAAS,CAClBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KACF,CAAC;QAED,IAAI0B,QAAQ,KAAK,KAAK,CAAC,EAAE;UACvBX,sBAAsB,CAAClB,IAAI,EAAE6B,QAAQ,CAAC;UACtC,IAAIG,OAAO;UAEX,IAAIhC,IAAI,KAAK,CAAC,EAAc;YAC1BgC,OAAO,GAAGH,QAAQ;UACpB,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAiB;YACpCgC,OAAO,GAAGH,QAAQ,CAACR,IAAI;YACvBZ,GAAG,GAAGoB,QAAQ,CAACpB,GAAG,IAAIN,KAAK,CAACM,GAAG;YAC/BC,GAAG,GAAGmB,QAAQ,CAACnB,GAAG,IAAIP,KAAK,CAACO,GAAG;YAE/BP,KAAK,GAAG;cAAEM,GAAG,EAAEA,GAAG;cAAEC,GAAG,EAAEA;YAAI,CAAC;UAChC,CAAC,MAAM;YACLP,KAAK,GAAG0B,QAAQ;UAClB;UAEA,IAAIG,OAAO,KAAK,KAAK,CAAC,EAAE;YACtB,IAAIX,IAAI,KAAK,KAAK,CAAC,EAAE;cACnBA,IAAI,GAAGW,OAAO;YAChB,CAAC,MAAM,IAAI,OAAOX,IAAI,KAAK,UAAU,EAAE;cACrCA,IAAI,GAAG,CAACA,IAAI,EAAEW,OAAO,CAAC;YACxB,CAAC,MAAM;cACLX,IAAI,CAAC1B,IAAI,CAACqC,OAAO,CAAC;YACpB;UACF;QACF;MACF;IACF;IAEA,IAAIhC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvD,IAAIqB,IAAI,KAAK,KAAK,CAAC,EAAE;QAEnBA,IAAI,GAAG,SAAAA,CAAUY,QAAQ,EAAEZ,IAAI,EAAE;UAC/B,OAAOA,IAAI;QACb,CAAC;MACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QACrC,IAAIa,eAAe,GAAGb,IAAI;QAE1BA,IAAI,GAAG,SAAAA,CAAUY,QAAQ,EAAEZ,IAAI,EAAE;UAC/B,IAAIlB,KAAK,GAAGkB,IAAI;UAEhB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,eAAe,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;YAC/C3B,KAAK,GAAG+B,eAAe,CAACJ,CAAC,CAAC,CAACnB,IAAI,CAACsB,QAAQ,EAAE9B,KAAK,CAAC;UAClD;UAEA,OAAOA,KAAK;QACd,CAAC;MACH,CAAC,MAAM;QACL,IAAIgC,mBAAmB,GAAGd,IAAI;QAE9BA,IAAI,GAAG,SAAAA,CAAUY,QAAQ,EAAEZ,IAAI,EAAE;UAC/B,OAAOc,mBAAmB,CAACxB,IAAI,CAACsB,QAAQ,EAAEZ,IAAI,CAAC;QACjD,CAAC;MACH;MAEAE,GAAG,CAAC5B,IAAI,CAAC0B,IAAI,CAAC;IAChB;IAEA,IAAIrB,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;QAC7BD,IAAI,CAACU,GAAG,GAAGN,KAAK,CAACM,GAAG;QACpBV,IAAI,CAACW,GAAG,GAAGP,KAAK,CAACO,GAAG;MACtB,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;MACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACU,GAAG,GAAGN,KAAK;MAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACW,GAAG,GAAGP,KAAK;MAClB;MAEA,IAAID,SAAS,EAAE;QACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;UAC7BuB,GAAG,CAAC5B,IAAI,CAAC,UAAUsC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOjC,KAAK,CAACM,GAAG,CAACE,IAAI,CAACsB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;UACFb,GAAG,CAAC5B,IAAI,CAAC,UAAUsC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOjC,KAAK,CAACO,GAAG,CAACC,IAAI,CAACsB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAe;UAClCuB,GAAG,CAAC5B,IAAI,CAACQ,KAAK,CAAC;QACjB,CAAC,MAAM;UACLoB,GAAG,CAAC5B,IAAI,CAAC,UAAUsC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOjC,KAAK,CAACQ,IAAI,CAACsB,QAAQ,EAAEG,IAAI,CAAC;UACnC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLT,MAAM,CAACU,cAAc,CAACb,IAAI,EAAE1B,IAAI,EAAEC,IAAI,CAAC;MACzC;IACF;EACF;EAEA,SAASuC,eAAeA,CAACf,GAAG,EAAEgB,KAAK,EAAEC,QAAQ,EAAE;IAC7C,IAAIC,iBAAiB;IACrB,IAAIC,kBAAkB;IAEtB,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IAEvC,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIL,OAAO,GAAGe,QAAQ,CAACV,CAAC,CAAC;MAGzB,IAAI,CAACgB,KAAK,CAACC,OAAO,CAACtB,OAAO,CAAC,EAAE;MAE7B,IAAIzB,IAAI,GAAGyB,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI3B,IAAI,GAAG2B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIvB,SAAS,GAAGuB,OAAO,CAACM,MAAM,GAAG,CAAC;MAElC,IAAI9B,QAAQ,GAAGD,IAAI,IAAI,CAAC;MACxB,IAAIwB,IAAI;MACR,IAAInC,YAAY;MAEhB,IAAIY,QAAQ,EAAE;QACZuB,IAAI,GAAGe,KAAK;QACZvC,IAAI,GAAGA,IAAI,GAAG,CAAC;QAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;UAC1B0C,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;UAC7CrD,YAAY,GAAGqD,kBAAkB;QACnC;MACF,CAAC,MAAM;QACLlB,IAAI,GAAGe,KAAK,CAACS,SAAS;QAEtB,IAAIhD,IAAI,KAAK,CAAC,EAAc;UAC1ByC,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;UAC3CpD,YAAY,GAAGoD,iBAAiB;QAClC;MACF;MAEA,IAAIzC,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;QACxC,IAAI+C,iBAAiB,GAAGhD,QAAQ,GAC5B4C,uBAAuB,GACvBF,sBAAsB;QAE1B,IAAIO,YAAY,GAAGD,iBAAiB,CAACxC,GAAG,CAACX,IAAI,CAAC,IAAI,CAAC;QAEnD,IACEoD,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiBlD,IAAI,KAAK,CAAE,IAC9CkD,YAAY,KAAK,CAAC,IAAiBlD,IAAI,KAAK,CAAE,EAC/C;UACA,MAAM,IAAIc,KAAK,CACb,uMAAuM,GACrMhB,IACJ,CAAC;QACH,CAAC,MAAM,IAAI,CAACoD,YAAY,IAAIlD,IAAI,GAAG,CAAC,EAAe;UACjDiD,iBAAiB,CAACvC,GAAG,CAACZ,IAAI,EAAEE,IAAI,CAAC;QACnC,CAAC,MAAM;UACLiD,iBAAiB,CAACvC,GAAG,CAACZ,IAAI,EAAE,IAAI,CAAC;QACnC;MACF;MAEAwB,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP3B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTb,YACF,CAAC;IACH;IAEA8D,gBAAgB,CAAC5B,GAAG,EAAEkB,iBAAiB,CAAC;IACxCU,gBAAgB,CAAC5B,GAAG,EAAEmB,kBAAkB,CAAC;EAC3C;EAEA,SAASS,gBAAgBA,CAAC5B,GAAG,EAAElC,YAAY,EAAE;IAC3C,IAAIA,YAAY,EAAE;MAChBkC,GAAG,CAAC5B,IAAI,CAAC,UAAUsC,QAAQ,EAAE;QAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,YAAY,CAAC0C,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CzC,YAAY,CAACyC,CAAC,CAAC,CAACnB,IAAI,CAACsB,QAAQ,CAAC;QAChC;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF;EAEA,SAASmB,cAAcA,CAAC7B,GAAG,EAAE8B,WAAW,EAAEC,SAAS,EAAE;IACnD,IAAIA,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI1C,YAAY,GAAG,EAAE;MACrB,IAAIkE,QAAQ,GAAGF,WAAW;MAC1B,IAAIvD,IAAI,GAAGuD,WAAW,CAACvD,IAAI;MAE3B,KAAK,IAAIgC,CAAC,GAAGwB,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9C,IAAIxC,oBAAoB,GAAG;UAAEkB,CAAC,EAAE;QAAM,CAAC;QAEvC,IAAI;UACF,IAAIgD,YAAY,GAAGF,SAAS,CAACxB,CAAC,CAAC,CAACyB,QAAQ,EAAE;YACxCvD,IAAI,EAAE,OAAO;YACbF,IAAI,EAAEA,IAAI;YACVP,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBACF;UACF,CAAC,CAAC;QACJ,CAAC,SAAS;UACRA,oBAAoB,CAACkB,CAAC,GAAG,IAAI;QAC/B;QAEA,IAAIgD,YAAY,KAAKpC,SAAS,EAAE;UAC9BF,sBAAsB,CAAC,EAAE,EAAcsC,YAAY,CAAC;UACpDD,QAAQ,GAAGC,YAAY;QACzB;MACF;MAEAjC,GAAG,CAAC5B,IAAI,CAAC4D,QAAQ,EAAE,YAAY;QAC7B,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,YAAY,CAAC0C,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CzC,YAAY,CAACyC,CAAC,CAAC,CAACnB,IAAI,CAAC4C,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF;EAoJA,OAAO,SAASE,iBAAiBA,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;IACpE,IAAI/B,GAAG,GAAG,EAAE;IACZe,eAAe,CAACf,GAAG,EAAE8B,WAAW,EAAEK,UAAU,CAAC;IAC7CN,cAAc,CAAC7B,GAAG,EAAE8B,WAAW,EAAEC,SAAS,CAAC;IAC3C,OAAO/B,GAAG;EACZ,CAAC;AACH;AAEA,IAAIkC,iBAAiB;AAEN,SAASE,aAAaA,CAACN,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;EACxEG,iBAAiB,GAAGA,iBAAiB,IAAItE,oBAAoB,CAAC,CAAC;EAC/D,OAAOsE,iBAAiB,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,CAAC;AAC9D", "ignoreList": []}