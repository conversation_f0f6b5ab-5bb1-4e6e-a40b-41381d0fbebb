{"version": 3, "names": ["_classApplyDescriptorGet", "receiver", "descriptor", "get", "call", "value"], "sources": ["../../src/helpers/classApplyDescriptorGet.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nexport default function _classApplyDescriptorGet(receiver, descriptor) {\n  if (descriptor.get) {\n    return descriptor.get.call(receiver);\n  }\n  return descriptor.value;\n}\n"], "mappings": ";;;;;;AAGe,SAASA,wBAAwBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACrE,IAAIA,UAAU,CAACC,GAAG,EAAE;IAClB,OAAOD,UAAU,CAACC,GAAG,CAACC,IAAI,CAACH,QAAQ,CAAC;EACtC;EACA,OAAOC,UAAU,CAACG,KAAK;AACzB", "ignoreList": []}